#!/usr/bin/env python3
"""
Test script to verify the "generate" parameter is being set correctly.
"""

import sys
import os
import unittest
from unittest.mock import patch, <PERSON><PERSON>

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import WorkflowUploader

class TestGenerateParameter(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures."""
        self.uploader = WorkflowUploader(
            url="https://test.example.com/",
            company_id="TEST",
            login_id="testuser",
            password="testpass"
        )
        
    @patch('main.requests.patch')
    def test_custom_upload_generate_true(self, mock_patch):
        """Test that custom uploads always use generate=true"""
        mock_response = Mock()
        mock_response.status_code = 201
        mock_patch.return_value = mock_response
        
        # Mock file reading
        with patch.object(self.uploader, 'read_from_file', return_value=("encoded_content", "description")):
            with patch('os.path.exists', return_value=True):
                result = self.uploader.upload_operation_or_flow("test", "operation", generate=True)
        
        # Verify the call was made with generate=true
        self.assertTrue(result)
        mock_patch.assert_called_once()
        call_args = mock_patch.call_args
        data = call_args[1]['data']  # Get the data parameter
        self.assertEqual(data['generate'], 'true')
        
    @patch('main.requests.patch')
    def test_standard_upload_generate_false(self, mock_patch):
        """Test that standard uploads (except last) use generate=false"""
        mock_response = Mock()
        mock_response.status_code = 201
        mock_patch.return_value = mock_response
        
        # Mock file reading
        with patch.object(self.uploader, 'read_from_file', return_value=("encoded_content", "description")):
            with patch('os.path.exists', return_value=True):
                result = self.uploader.upload_operation_or_flow("test", "operation", generate=False)
        
        # Verify the call was made with generate=false
        self.assertTrue(result)
        mock_patch.assert_called_once()
        call_args = mock_patch.call_args
        data = call_args[1]['data']  # Get the data parameter
        self.assertEqual(data['generate'], 'false')
        
    @patch('main.requests.patch')
    def test_standard_upload_last_generate_true(self, mock_patch):
        """Test that the last standard upload uses generate=true"""
        mock_response = Mock()
        mock_response.status_code = 201
        mock_patch.return_value = mock_response
        
        # Mock file reading
        with patch.object(self.uploader, 'read_from_file', return_value=("encoded_content", "description")):
            with patch('os.path.exists', return_value=True):
                result = self.uploader.upload_operation_or_flow("masterdataupdate", "flow", generate=True)
        
        # Verify the call was made with generate=true
        self.assertTrue(result)
        mock_patch.assert_called_once()
        call_args = mock_patch.call_args
        data = call_args[1]['data']  # Get the data parameter
        self.assertEqual(data['generate'], 'true')

    def test_generate_parameter_in_data_object(self):
        """Test that the generate parameter is correctly added to the data object"""
        # Test with generate=True
        with patch.object(self.uploader, 'read_from_file', return_value=("encoded_content", "description")):
            with patch('os.path.exists', return_value=True):
                with patch('main.requests.patch') as mock_patch:
                    mock_response = Mock()
                    mock_response.status_code = 201
                    mock_patch.return_value = mock_response
                    
                    self.uploader.upload_operation_or_flow("test", "operation", generate=True)
                    
                    # Check the data object structure
                    call_args = mock_patch.call_args
                    data = call_args[1]['data']
                    
                    # Verify all expected fields are present
                    expected_fields = [
                        'wfType', 'wfName', 'wfSubType', 'wfNamespace', 
                        'wfTitle', 'wfDescription', 'wfContent', 'generate'
                    ]
                    for field in expected_fields:
                        self.assertIn(field, data)
                    
                    # Verify generate field specifically
                    self.assertEqual(data['generate'], 'true')
                    self.assertEqual(data['wfType'], 'operation')
                    self.assertEqual(data['wfName'], 'test')

if __name__ == '__main__':
    print("Testing generate parameter implementation...")
    unittest.main(verbosity=2)
