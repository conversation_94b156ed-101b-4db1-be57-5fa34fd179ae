#!/usr/bin/env python3
"""
Test script to verify SSL error handling in the WorkflowUploader.
This script tests various SSL scenarios to ensure proper error handling.
"""

import sys
import os
import unittest
from unittest.mock import patch, Mock
import requests
from requests.exceptions import SSLError, ConnectionError, Timeout, RequestException

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import WorkflowUploader

class TestSSLHandling(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures."""
        self.uploader = WorkflowUploader(
            url="https://test.example.com/",
            company_id="TEST",
            login_id="testuser",
            password="testpass"
        )
        
    def test_ssl_verification_enabled_by_default(self):
        """Test that SSL verification is enabled by default for HTTPS URLs."""
        uploader = WorkflowUploader(
            url="https://test.example.com/",
            company_id="TEST",
            login_id="testuser",
            password="testpass"
        )
        self.assertTrue(uploader.verify_ssl)
        
    def test_ssl_verification_disabled_for_localhost(self):
        """Test that SSL verification is disabled for localhost."""
        uploader = WorkflowUploader(
            url="https://localhost:8080/",
            company_id="TEST",
            login_id="testuser",
            password="testpass"
        )
        self.assertFalse(uploader.verify_ssl)
        
    def test_ssl_verification_disabled_for_127_0_0_1(self):
        """Test that SSL verification is disabled for 127.0.0.1."""
        uploader = WorkflowUploader(
            url="https://127.0.0.1:8080/",
            company_id="TEST",
            login_id="testuser",
            password="testpass"
        )
        self.assertFalse(uploader.verify_ssl)
        
    def test_ssl_verification_explicitly_disabled(self):
        """Test that SSL verification can be explicitly disabled."""
        uploader = WorkflowUploader(
            url="https://test.example.com/",
            company_id="TEST",
            login_id="testuser",
            password="testpass",
            disable_ssl_verification=True
        )
        self.assertFalse(uploader.verify_ssl)
        
    @patch('main.requests.get')
    def test_ssl_error_handling_in_authenticate(self, mock_get):
        """Test that SSL errors are properly handled in authenticate method."""
        mock_get.side_effect = SSLError("SSL certificate verify failed")
        
        result = self.uploader.authenticate()
        
        self.assertFalse(result)
        self.assertIn("SSL Certificate Error", self.uploader.error)
        self.assertIn("Consider using a trusted certificate", self.uploader.error)
        
    @patch('main.requests.get')
    def test_connection_error_handling_in_authenticate(self, mock_get):
        """Test that connection errors are properly handled in authenticate method."""
        mock_get.side_effect = ConnectionError("Connection refused")
        
        result = self.uploader.authenticate()
        
        self.assertFalse(result)
        self.assertIn("Connection Error", self.uploader.error)
        
    @patch('main.requests.get')
    def test_timeout_error_handling_in_authenticate(self, mock_get):
        """Test that timeout errors are properly handled in authenticate method."""
        mock_get.side_effect = Timeout("Request timed out")
        
        result = self.uploader.authenticate()
        
        self.assertFalse(result)
        self.assertIn("Request Timeout", self.uploader.error)
        
    @patch('main.requests.get')
    def test_non_json_response_handling_in_authenticate(self, mock_get):
        """Test that non-JSON responses are properly handled in authenticate method."""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.json.side_effect = ValueError("No JSON object could be decoded")
        mock_response.text = "<html><body>Internal Server Error</body></html>"
        mock_get.return_value = mock_response
        
        result = self.uploader.authenticate()
        
        self.assertFalse(result)
        self.assertIn("HTTP 500", self.uploader.error)
        self.assertIn("Internal Server Error", self.uploader.error)

if __name__ == '__main__':
    print("Testing SSL error handling in WorkflowUploader...")
    unittest.main(verbosity=2)
