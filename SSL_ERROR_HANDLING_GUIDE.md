# SSL Error Handling & Upload Improvements Guide

## Overview
This guide explains the enhanced SSL error handling and upload improvements made to the Workflow Uploader application to resolve "Bad Gateway" and SSL certificate issues.

## 🔧 **New Features**

### 1. **Enhanced SSL Error Handling**
- **Automatic SSL bypass** for localhost and 127.0.0.1
- **Optional SSL verification disable** for self-signed certificates
- **Clear error messages** for different SSL scenarios
- **Graceful handling** of non-JSON responses (HTML error pages)

### 2. **Upload Rate Limiting & Retry Logic**
- **Configurable delays** between uploads (0.5-5 seconds)
- **Automatic retry** for failed uploads (up to 2 retries)
- **Exponential backoff** for retry attempts
- **Specific handling** for "Bad Gateway" (502) errors

### 3. **Better Progress Tracking**
- **Real-time progress** indicators during standard uploads
- **Upload counters** showing current file being processed
- **Success/failure summary** at the end
- **Failed file tracking** with error reasons

## 🚀 **How to Use**

### For Standard Uploads (Multiple Files)
1. **Set Upload Delay**: Use the slider to set delay between uploads (recommended: 1.5-2 seconds)
2. **Monitor Progress**: Watch real-time progress as files are uploaded
3. **Review Summary**: Check the final summary for any failed uploads

### For SSL Certificate Issues
1. **Check the SSL checkbox** if you're connecting to a server with self-signed certificates
2. **Warning will appear** to remind you about security implications
3. **Only use in development/testing** environments

## 🛠 **Technical Improvements**

### Error Handling
```python
# Before: Would crash on non-JSON responses
response.json()['error']

# After: Graceful handling of HTML error pages
try:
    error_detail = response.json()['error']
except (ValueError, KeyError):
    error_detail = f"HTTP {response.status_code}: {response.text[:200]}"
```

### Retry Logic
- **Connection errors**: Automatically retried up to 2 times
- **Timeout errors**: Automatically retried up to 2 times  
- **Bad Gateway (502)**: Specifically handled with retries
- **Other HTTP errors**: Not retried (likely permanent issues)

### Rate Limiting
- **Default delays**: 1.5s for standard uploads, 0.5s for custom uploads
- **Configurable**: Users can adjust from 0.5s to 5s
- **Prevents server overload** that causes "Bad Gateway" errors

## 📊 **Upload Summary Features**

The application now provides detailed summaries:
- ✅ **Successful uploads**: Count and list
- ❌ **Failed uploads**: Count, list, and error reasons
- 📊 **Overall statistics**: Success rate and completion status

## 🔍 **Common Error Scenarios & Solutions**

### "Bad Gateway" Errors
**Cause**: Server overload from too many rapid requests
**Solution**: Increase upload delay to 2-3 seconds

### SSL Certificate Errors
**Cause**: Self-signed or untrusted certificates
**Solution**: Enable "Disable SSL Certificate Verification" checkbox

### Connection Timeouts
**Cause**: Network issues or slow server response
**Solution**: Automatic retry logic handles this (no user action needed)

### File Not Found Errors
**Cause**: Missing workflow files in Content directory
**Solution**: Ensure all required .sl files exist in the correct directories

## ⚙️ **Configuration Options**

### Frontend Controls
- **SSL Verification Checkbox**: Disable for self-signed certificates
- **Upload Delay Slider**: Control rate limiting (0.5-5 seconds)
- **Upload Type**: Standard (all files) vs Custom (single file)

### Backend Parameters
- `disableSSLVerification`: Boolean to disable SSL checks
- `uploadDelay`: Float value for delay between uploads
- `max_retries`: Number of retry attempts (default: 2)

## 🔒 **Security Considerations**

### SSL Verification Disable
- **Only use for development/testing**
- **Warning message** displayed when enabled
- **Not recommended for production** environments

### Rate Limiting Benefits
- **Prevents server overload**
- **Reduces "Bad Gateway" errors**
- **Improves overall reliability**

## 📝 **Logging & Debugging**

Enhanced logging now includes:
- SSL verification status
- Upload attempt numbers
- Retry information
- Detailed error messages
- Progress tracking

Check the console logs for detailed information about upload progress and any issues.
