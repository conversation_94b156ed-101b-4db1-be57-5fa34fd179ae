<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<jardesc>
    <jar path="/Users/<USER>/SourceCode/UMP5Share/pa/UNVIRED.APP352164853511/LOCAL_TURBO_APPS_CONTENT.jar"/>
    <options buildIfNeeded="true" compress="true" descriptionLocation="/WorkflowContent/makeexportjar.jardesc" exportErrors="true" exportWarnings="true" includeDirectoryEntries="true" overwrite="true" saveDescription="true" storeRefactorings="false" useSourceFolders="false"/>
    <storedRefactorings deprecationInfo="true" structuralOnly="false"/>
    <selectedProjects/>
    <manifest generateManifest="true" manifestLocation="/CUMP_HZ_Redis/META-INF/services/com.hazelcast.spi.discovery.DiscoveryStrategyFactory" manifestVersion="1.0" reuseManifest="false" saveManifest="false" usesManifest="true">
        <sealing sealJar="false">
            <packagesToSeal/>
            <packagesToUnSeal/>
        </sealing>
    </manifest>
    <selectedElements exportClassFiles="true" exportJavaFiles="false" exportOutputFolder="false">
        <javaElement handleIdentifier="=WorkflowContent/src"/>
    </selectedElements>
</jardesc>
