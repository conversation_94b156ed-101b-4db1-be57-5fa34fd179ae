package com.unvired.operation;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import com.api.jsonata4java.expressions.Expressions;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.node.MissingNode;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.common.WorkflowCacheUtils;
import com.unvired.evaluator.BigString;
import com.unvired.evaluator.Evaluator;
import com.unvired.evaluator.EvaluatorBuilder;


public class CalculateRequest extends AbstractWorkflowRequest
{
    private final int MAX_RESULTS = 50;     // Just to keep it sane
    private final String DELIMITER = "|";

    @Action(name = "calculate",
            outputs = 
        {
                @Output("result"),
                @Output("systemresult"),
                @Output("error")
        },
        responses = 
    {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
    }
            )

    public Map<String, String> calculate(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "formdata", required = false) String data,
            @Param(value = "formusers", required = false) String formusers,
            @Param(value = "priorresults", required = false) String priorResults,            
            @Param(value = "flowinput", required = false) String flowInput,
            @Param(value = "expressions", required = true) String expressions)
    {
        Map<String, String> results = new HashMap<>();
        String error = "";
        StringBuffer systemResult = null;
        
        try
        {
            init(convId);
            
            logInfo("calculate", "Preparing call, convId: " + convId);
            logDebug("calculate", "formdata: " + data);
            if (flowInput != null)
                logDebug("calculate", "flowinput: " + flowInput);
            
            // If prior results has the placeholder ~Will~be~CHANGEd~ replace it with just double quotes to make teh json
            if (priorResults != null)
                priorResults = priorResults.replace("~Not~FOUND~", "\"\"");
    
            if (formusers != null)
                logDebug("calculate", "formusers: " + formusers);
            logDebug("calculate", "priorresults: " + priorResults);
            logDebug("calculate", "expressions: " + expressions);
    
            // Retrive variables from cache .. incrementally variable/result will keep getting added to it
            Map<String, Object> varsInCache = WorkflowCacheUtils.getVariables(convId);
            Map<String, String> fieldsType = WorkflowCacheUtils.getFields(convId); 

            ObjectMapper mapper = new ObjectMapper();
            ArrayList<JsonNode> expressionsArray = mapper.readValue(expressions, new TypeReference<ArrayList<JsonNode>>() {});
            if (expressionsArray.size() > MAX_RESULTS)
                throw new Evaluator.ExpressionException("A max of " + MAX_RESULTS + " expressions are supported in a batch");

            JsonObject jsonResult = new JsonObject();

            for (JsonNode node : expressionsArray) 
            {
                String variable = node.get("key").asText();
                String variableType = node.get("type") == null ? "string" : node.get("type").asText();
                String expression = node.get("value") != null ? node.get("value").asText() : "";
                String category = node.get("category").asText();
                String jsonataExpr = node.get("mapexpression") != null ? node.get("mapexpression").asText() : "";
                
                logDebug("calculate", "category: " + category + ", variable: " + variable + 
                                        ", variabletype: " + variableType + ", expression: " + expression +
                                        ", mapexpression: " + jsonataExpr);

                // Standard expression or JSONata?
                if ("expression".equalsIgnoreCase(category))
                {
                    // Handle one expression at a time to calculate
                    Evaluator evaluator = new EvaluatorBuilder().buildFromExpression(alias, convId, expression, process.getLogger());

                    // Fill the variables
                    JsonNode root = mapper.readTree(data);
                    List<String> usedVars = null;
                    try {
                        usedVars = evaluator.getUsedVariables();
                    } catch (Exception e) { usedVars = new ArrayList<String>(); };
                    
                    for (String var: usedVars)
                    {
                        try
                        {
                            logDebug("calculate", "*************** variable: " + var);
                            if ("formdata".equalsIgnoreCase(var))
                            {
                                evaluator.with(var, evaluator.createSafeString(data));
                            }
                            else if ("flowinput".equalsIgnoreCase(var))
                            {
                                evaluator.with(var, evaluator.createSafeString(flowInput));
                                logDebug("calculate", "+++++++++++++++ value: " + flowInput);
                            }
                            else if (var.startsWith("env_")) {
                                String envVar = var.substring(4);
                                if (envVars != null && envVars.containsKey(envVar)) {
                                    logDebug("calculate", "+++++++++++++++ environment value: " + envVars.get(envVar));
                                    evaluator.with(var, evaluator.createSafeString(envVars.get(envVar)));
                                }
                                else {
                                    logInfo("calculate", "+++++++++++++++ environment value not found");
                                    evaluator.with(var, "");
                                }
                            }
                            else 
                            {
                                String varType = "string";
                                if (fieldsType.get(var) != null)
                                    varType = fieldsType.get(var);
                                else
                                    logInfo("calculate", "Field type defaulted to string for: " + var);

                                String jsonPath = "/" + var.replace(".", "/");          // search is via / and not .
                                logDebug("calculate", "Looking for: " + jsonPath + " to get:" + var);
                                JsonNode token = root.at(jsonPath);
                                if (!(token instanceof MissingNode))
                                {
                                    if (token.isArray())
                                    {
                                        ObjectReader reader = mapper.readerFor(new TypeReference<ArrayList<Number>>() {});
                                        ArrayList<Number> numbers = reader.readValue(token);
                                        evaluator.with(var, numbers);
                                        logDebug("calculate", "+++++++++++++++ value: " + numbers);
                                    }
                                    else
                                    {
                                        if (varType.equals("number") || varType.equals("integer") || varType.equals("double"))
                                            evaluator.with(var, new BigDecimal(token.asText()));
                                        else
                                            evaluator.with(var, evaluator.createSafeString(token.asText()));
                                        logDebug("calculate", "+++++++++++++++ value: " + token.asText());
                                    }
                                } else {
                                    logDebug("calculate", "Var: " + var + " not found in formdata. Setting to empty");
                                    if (varType.equals("number") || varType.equals("integer") || varType.equals("double"))
                                        evaluator.with(var, new BigDecimal("0"));
                                    else
                                        evaluator.with(var, "");
                                }
                            }
                        }
                        catch (Exception ex) { 
                            ex.printStackTrace();
                        }
                    }

                    // Add the prior results .. input is a JSON of results name and value
                    logInfo("calculate", "extracting prior results");
                    if (priorResults != null && !priorResults.isEmpty()) {
                        JsonNode rsltArray = mapper.readTree(priorResults);
                        Iterator<String> it = rsltArray.fieldNames();
                        while (it.hasNext())
                        {
                            String key = it.next();
                            String value = rsltArray.findValue(key).toString();
                            
                            // Is it a script or a different node?  If result key exists then its considered to be a different step and should be used as is
                            try {
                                JsonNode valuetree = mapper.readTree(value);
                                // Is this the only key and is it result?
                                if (valuetree.has("result") && valuetree.size() == 1) {
                                    evaluator.with(key, evaluator.createSafeString(value));                     // Workflow saves it as step_result
                                    logDebug("calculate", "*************** key: " + key + ", value: " + value);
                                } else {
                                    boolean textNode = true;
                                    Iterator<String> itval = valuetree.fieldNames();
                                    while (itval.hasNext()) {
                                        key = itval.next();
                                        boolean lazyNum = false;
                                        JsonNode vnode = valuetree.findValue(key);
                                        if (vnode.isTextual()) {
                                            lazyNum = true;
                                            value = vnode.asText();
                                        }
                                        else
                                            value = vnode.toString();
                                        
                                        // Possibly a script node, skip error as a variable
                                        if (!key.equalsIgnoreCase("error")) {
                                            if (lazyNum)
                                                evaluator.with(key, evaluator.createSafeString(value));
                                            else
                                                evaluator.with(key, value);
                                            logDebug("calculate", "*************** key: " + key + ", value: " + value);
                                        }
                                        textNode = false;           // not a single textnode
                                    }
                                    
                                    // If this was a single text node? (strings like "helloworld" are also treated as valid JSON by Jackson)
                                    if (textNode) {
                                        evaluator.with(key, evaluator.createSafeString(value));
                                        logDebug("calculate", "*************** key: " + key + ", value: " + value);
                                    }
                                }
                            } catch (Exception e) {
                                evaluator.with(key, value);                                // just add the value
                                logDebug("calculate", "*************** key: " + key + ", value: " + value);
                            }                            
                        }
                    }

                    // Add the results from the expressions calculated so far using their variable names as they may be used in later expressions
                    for (Entry<String, Object> entry : varsInCache.entrySet()) {
                        if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                            Object val = entry.getValue();
                            if (val instanceof BigDecimal) {
                                evaluator.with(entry.getKey(), (BigDecimal) val);
                            } else {
                                evaluator.with(entry.getKey(), evaluator.createSafeString(val.toString()));
                            }
                        }
                    }

                    // Evaluate (if numerical expression zeros are trimmed else BigString is returned)
                    logInfo("calculate", "Evaluating");
                    BigDecimal result = evaluator.eval(evaluator.isTrimZeroes());
                    String strResult = null;
                    
                    // We need to decide the required type based on what the workflow is configured with
                    // Add the result to both the indexed output, json and the variable cache so that they can be used as vars
                    if ("number".equalsIgnoreCase(variableType)) {
                        if (result instanceof BigString)
                            result = new BigDecimal(((BigString) result).toString());
                        
                        jsonResult.addProperty(variable, result);
                        varsInCache.put(variable, result);
                    } else {                                                        // Add as String
                        if (result instanceof BigDecimal)
                            strResult = ((BigDecimal) result).toPlainString();
                        else
                            strResult = ((BigString) result).toString();
                        
                        varsInCache.put(variable, strResult);
                        jsonResult.addProperty(variable, strResult);
                    }
                        
                    // Add the system result with all calculated values
                    String tempResult = strResult;
                    if (tempResult == null)
                        tempResult = result.toPlainString();
                    
                    if (systemResult == null)
                        systemResult = new StringBuffer(tempResult);
                    else
                        systemResult.append(DELIMITER + tempResult);

                    logDebug("calculate", variable + " + " + (strResult == null ? result.toPlainString() : strResult));
                }
                else {      // This is a JSONata expression
                    // Read the JSON data into a JsonNode for JSONata to process .. first check formdata, variables and previous results
                    JsonNode jsonataVar = null;   // mapper.readTree(data).at(jsonPath); 
                    if (!"${formdata}".equalsIgnoreCase(expression)) {              // Case 1: Not formdata
                        if (!"${custominput}".equalsIgnoreCase(expression)) {   // Case 2: Not flowinput
                            if (!"${formusers}".equalsIgnoreCase(expression)) { // Case 3: Not formusers 
                                // Get in variables
                                jsonataVar = null;
                                String jsonPath = expression.replace("${", "").replace("}", "");
                                String calcVar = jsonPath;
                                if (calcVar.contains("_") && !calcVar.startsWith("env_") && !calcVar.endsWith("_result")) {
                                    calcVar = calcVar.substring(calcVar.indexOf("_")+1);
                                }
                                if (!varsInCache.containsKey(calcVar)) {        // Case 4: Not in previous calculations
                                    if (priorResults != null && !priorResults.isEmpty()) {  // Case 5: Check previous results
                                        JsonNode rsltArray = mapper.readTree(priorResults);
                                        Iterator<String> it = rsltArray.fieldNames();
                                        while (it.hasNext())
                                        {
                                            String key = it.next();
                                            if (key.equalsIgnoreCase(jsonPath)) {
                                                jsonataVar = mapper.readTree(rsltArray.findValue(key).toString());
                                                break;
                                            }
                                        }
                                    }
                                }
                                else {
                                    // Assume string and read it out
                                    jsonataVar = mapper.readTree(varsInCache.get(calcVar).toString());
                                }

                                if (jsonataVar == null) {                                    // Case 6: Field in formdata
                                    jsonPath = expression.replace("${", "").replace("}", "");
                                    jsonPath = "/" + jsonPath.replace(".", "/");          // search is via / and not .
                                    logDebug("calculate", "Looking for formfield: " + jsonPath);
                                    JsonNode root = mapper.readTree(data);
                                    JsonNode token = root.at(jsonPath);
                                    if (!token.isMissingNode())
                                        jsonataVar = token; 
                                }
                                
                                // Variable not found!!!!
                                if (jsonataVar == null)
                                    throw new Exception("Cannot extract value of: " + expression + " from formdata, calculated expressions, previous workflow results or form fields");
                            }
                            else {
                                jsonataVar = mapper.readTree(formusers);
                            }
                        }
                        else {
                            jsonataVar = mapper.readTree(flowInput);
                        }
                    }
                    else {
                        jsonataVar = mapper.readTree(data);
                    }
                    
                    // Parse the JSONata expression
                    Expressions jsonata = Expressions.parse(jsonataExpr);
                    JsonNode result = jsonata.evaluate(jsonataVar);
                    
                    // What kind of a result do we have?  JSON or others?
                    String strResult = "";
                    if (result != null) {
                        
                        boolean numberRequired = "number".equalsIgnoreCase(variableType);
                        if (result.isContainerNode()) {
                            varsInCache.put(variable, mapper.writeValueAsString(result));
                            strResult = mapper.writeValueAsString(result);
                        }  else {
                            if (!result.isNumber()) {
                                
                                // We need a text .. we can do that directly or after conversion based on the configured type in workflow
                                if (numberRequired) {
                                    BigDecimal tempResult = new BigDecimal(result.asText());
                                    varsInCache.put(variable, tempResult);
                                    jsonResult.addProperty(variable, tempResult);
                                } else {
                                    varsInCache.put(variable, result.asText());
                                    jsonResult.addProperty(variable, result.asText());
                                }
                            }
                            else {
                                boolean addAsText = false;
                                if (result.isInt()) {
                                    if (numberRequired) {
                                        varsInCache.put(variable,  new BigDecimal(result.asInt()));
                                        jsonResult.addProperty(variable, result.asInt());
                                    } else {
                                        addAsText = true;
                                    }
                                }
                                else if (result.isLong()) {
                                    if (numberRequired) {
                                        varsInCache.put(variable,  new BigDecimal(result.asLong()));
                                        jsonResult.addProperty(variable, result.asLong());
                                    }  else {
                                        addAsText = true;
                                    }
                                }
                                else {
                                    if (numberRequired) {
                                        varsInCache.put(variable,  new BigDecimal(result.asDouble()));
                                        jsonResult.addProperty(variable, result.asDouble());
                                    }  else {
                                        addAsText = true;
                                    }
                                }
                                
                                // If number but needed as text handle it.
                                if (addAsText) {
                                    varsInCache.put(variable,  result.asText());
                                    jsonResult.addProperty(variable, result.asText());
                                }
                            }
                            strResult = result.asText();
                        }
                        logDebug("calculate",  "Extracted data: " + result.asText());
                    } else {
                        logInfo("calculate",  "Could not extract data, returning empty string");
                    }
                    
                    if (systemResult != null)
                        systemResult.append(DELIMITER + strResult);
                    else
                        systemResult = new StringBuffer(strResult);

                    logDebug("calculate", "variable: " + variable + ", " + result.asText());
                }
            }

            // All done - set json result for workflow log and troubleshooting
            results.put("systemresult", systemResult.toString());
            results.put("result", new Gson().toJson(jsonResult));
            logInfo("calculate", "Success call end");
        }
        catch (Exception e)
        {
            e.printStackTrace();
            results.put("systemresult", "");
            results.put("result", "");
            error = "Expresssion error: " + e.getMessage();
        }

        results.put("error", error);

        if (!error.isEmpty())
            logError("evaluate", error);
        
        return  results;
    }
}
