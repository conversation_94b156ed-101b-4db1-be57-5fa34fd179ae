package com.unvired.operation;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import com.google.api.client.util.Base64;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.ump.attachment.AttachmentMeta;
import com.unvired.ump.attachment.AttachmentSource;
import com.unvired.ump.attachment.AttachmentState;
import com.unvired.ump.attachment.IAttachment;
import org.apache.commons.io.IOUtils;

public class File<PERSON>acher extends AbstractWorkflowRequest
{
    @Action(name = "filestore",
            outputs = 
        {
                @Output("result"),
                @Output("error")
        },
        responses = 
    {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
    }
            )

    public Map<String, String> cache(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "operation", required = true) String operation,
            @Param(value = "format", required = true) String format,
            @Param(value = "attachmentid", required = false) String attId,
            @Param(value = "externalreference", required = false) String externalRef,
            @Param(value = "filename", required = false) String filename,
            @Param(value = "mimetype", required = false) String mimeType,
            @Param(value = "data", required = false) String data)
    {
        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";

        try
        {
            init(convId);
            logInfo("filecacher", "Parsing input data");
            logDebug("filecacher", "operation: " + operation);
            logDebug("filecacher", "format: " + format);

            String inputData = data;
            logDebug("filecacher", "sourcedata original: " + (String) (inputData != null ? inputData : ""));
            logDebug("filecacher", "attachmentid: " + (String) (attId != null ? attId : ""));
            logDebug("filecacher", "filename: " + (String) (filename != null ? filename : ""));
            logDebug("filecacher", "mimetype: " + (String) (mimeType != null ? mimeType : ""));
            logDebug("filecacher", "externalreference: " + (String) (externalRef != null ? externalRef : ""));
            
            if (operation.equalsIgnoreCase("DOWNLOAD")) {
                if (attId == null && externalRef == null) {
                    error = "To download a file from cache, attachmentid or externalreference need to be specified";
                } else {
                    // Get the attachment and set the file stream
                    IAttachment att = null;
                    if (attId != null)
                        att = process.getAttachmentService().getAttachmentByID(attId);
                    else {
                        List<IAttachment> atts = process.getAttachmentService().getAttachmentByExternalReference(externalRef);
                        if (atts.size() > 1)
                            error = "Found multiple attachments in cache with the same external reference, cannot identify attachment uniquely";
                        else
                            att = atts.get(0);
                    }
                    
                    if (att == null) {
                        if (error.isEmpty())        // by external ref the error may already be set
                            error = "Attachmend not found in cache.";
                    } else if (att.getState() != AttachmentState.CACHED) {
                        error = "Attachmend not yet cached, please try later.";
                    } else {
                        // Get the file from attachment cache adn copy it to ftprequest
                        InputStream stream = process.getAttachmentService().readAttachment(att.getUid());
                        if (format.equalsIgnoreCase("BASE64")) {
                            byte bytes[] = IOUtils.toByteArray(stream);
                            dataRows = new String(Base64.encodeBase64(bytes));
                        } else {
                            dataRows = IOUtils.toString(stream, "utf-8");
                        }
                        stream.close();
                    }
                }
            } else if (operation.equalsIgnoreCase("UPLOAD")){
                if (inputData == null) {
                    error = "To upload file to cache, data needs to be specified";
                } else {
                    String uid = UUID.randomUUID().toString();
                    AttachmentMeta meta = new AttachmentMeta();
                    meta.setUid(uid);
                    meta.setCache(true);
                    meta.setFileName(filename);
                    meta.setDescription("Uploaded via Workflow");
                    meta.setMimeType(mimeType);
                    meta.setSource(AttachmentSource.Workflow);
                    if (externalRef == null) externalRef = uid; 
                    meta.setExternalReference(externalRef);
                    IAttachment newAtt = process.getAttachmentService().addAttachment(meta);
                    
                    ByteArrayInputStream stream = null;
                    
                    if (format.equalsIgnoreCase("BASE64"))
                        stream = new ByteArrayInputStream(Base64.decodeBase64(data.getBytes("utf-8")));
                    else
                        stream = new ByteArrayInputStream(data.getBytes("utf-8"));
                    
                    process.getAttachmentService().loadAttachment(newAtt.getUid(), stream);
                    stream.close();
                    dataRows = newAtt.getUid();
                }
            } else if (operation.equalsIgnoreCase("DELETE")) {
                if (attId == null && externalRef == null) {
                    error = "To delete a file from cache, attachmentid or externalreference need to be specified";
                } else {
                    // Get the attachment and set the file stream
                    IAttachment att = null;
                    if (attId != null)
                        att = process.getAttachmentService().getAttachmentByID(attId);
                    else {
                        List<IAttachment> atts = process.getAttachmentService().getAttachmentByExternalReference(externalRef);
                        if (atts.size() > 1)
                            error = "Found multiple attachments in cache with the same external reference, cannot identify attachment uniquely";
                        else
                            att = atts.get(0);
                    }
                    
                    if (att == null) {
                        if (error.isEmpty())        // by external ref the error may already be set
                            error = "Attachmend not found in cache.";
                    } else if (att.getState() != AttachmentState.CACHED) {
                        error = "Attachmend not yet cached, please try later.";
                    } else {
                        process.getAttachmentService().deleteAttachmentByID(att.getUid());
                        dataRows = "Deleted attachment";
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            results.put("result", "");
            error = "File Cacher error: " + e.getMessage();
        }

        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("filecacher", "Data transformed successfully, returning results");
        else
            logError("filecacher", error);
        
        return  results;
    }
}
