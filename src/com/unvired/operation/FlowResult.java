package com.unvired.operation;

import java.util.HashMap;
import java.util.Map;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.common.WorkflowCacheUtils;

public class FlowResult extends AbstractWorkflowRequest
{
    @Action(name = "response",
        outputs = {
                @Output("finalResult"),
                @Output("finalError")
        },
        responses = {
                @Response(text = "success", field = "finalError", value = "", matchType = MatchType.COMPARE_EQUAL, isDefault = true),
                @Response(text = "failure", field = "finalError", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isOnFail = true)
        }
    )
    
    public Map<String, String> setResult(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "outputdata", required = false) String outputData,
            @Param(value = "outputerror", required = false) String outputError)    
    {
        
        Map<String, String> results = new HashMap<>();
        try 
        {
            init(convId);
            
            logInfo("setResult", "Preparing");
            logDebug("setResult", "outputdata: " + outputData != null ? outputData : "");
            logDebug("setResult", "outputerror: " + outputError != null ? outputError : "");
            
            if (outputData != null && !outputData.isEmpty()) {
                logInfo("setResult", "Registering output data for conversation: " + convId);
                logDebug("setResult", outputData);
            }
            
            if (outputError != null && !outputError.isEmpty()) {
                logError("setResult", "Registering error for conversation: " + convId + ".  Error details: " + outputError);
            }
            
            // Add it to the hash map via conversationID also
            WorkflowCacheUtils.putResult(convId, outputData, outputError);
    
            results.put("finalResult", outputData != null ? outputData : "");
            results.put("finalError", outputError != null ? outputError : "");
        } catch (Exception e) {
            e.printStackTrace();
            results.put("finalResult", "");
            results.put("finalError", e.getMessage());
        }
        
        return  results;
    }
}
