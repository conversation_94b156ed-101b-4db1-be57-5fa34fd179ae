package com.unvired.operation;

import java.net.URL;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import org.joda.time.LocalDateTime;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.odata.meta.Navigation;
import com.unvired.odata.pojo.Entity;
import com.unvired.odata.workflow.WFODataEntity;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.IODataRequest;
import com.unvired.ump.agent.IODataRequest.Operation;
import com.unvired.ump.agent.IODataResponse;

public class ODataRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_ODATA = "ADHOC_ODATA_PORT_ODATA_EXECUTE";

    @Action(name = "odata", outputs = { @Output("result"), @Output("error") }, responses = { @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL), @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true) })

    public Map<String, String> execute(@Param(value = "alias", required = true) String alias, 
            @Param(value = "namespace", required = true) String namespace, 
            @Param(value = "application", required = true) String application, 
            @Param(value = "convId", required = true) String convId, 
            @Param(value = "system", required = true) String system, 
            @Param(value = "metadata", required = true) String metadata, 
            @Param(value = "operation", required = true) String operation, 
            @Param(value = "entity", required = true) String entity, 
            @Param(value = "entitydata", required = false) String entityData, 
            @Param(value = "filter", required = false) String filter, 
            @Param(value = "parameters", required = false) String parameters, 
            @Param(value = "expand", required = false) String expand,
            @Param(value = "errormessageonnodata", required = false) String errorMsgOnNoData)
    {

        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";
        Map<String, String> params = null;
        Entity[] allEntities = null;
        WFODataEntity oentity = null;
        Operation opn = null;
        IODataRequest odataRequest = null;
        
        // Parse the inputs json for the OData entities
        try
        {
            init(convId);

            logInfo("execute", "Processing Input");
            logDebug("execute", "metadata: " + metadata);
            logDebug("execute", "operation: " + operation);
            logDebug("execute", "entity: " + entity);
            logDebug("execute", "entitydata: " + (String) (entityData != null ? entityData : ""));
            logDebug("execute", "filter: " + (String) (filter != null ? filter : ""));
            logDebug("execute", "parameters: " + (String) (parameters != null ? parameters : ""));
            logDebug("execute", "expand: " + (String) (expand != null ? expand : ""));
            logDebug("execute", "errormessageonnodata: " + (String) (errorMsgOnNoData != null ? errorMsgOnNoData : ""));

            // Get the operation
            odataRequest = (IODataRequest) process.createRequest(RequestType.OData);
            opn = Enum.valueOf(Operation.class, operation);

            logInfo("execute", "Processing metadata");
            ObjectMapper mapper = new ObjectMapper();
            allEntities = mapper.readValue(metadata, new TypeReference<Entity[]>() { });
            if (allEntities.length == 0)
                throw new Exception("No OData entities specified.");

            // Cache them for later use in the adapter
            for (Entity e : allEntities)
            {
                WFODataEntity wfe = e.toODataEntity();
                odataRequest.cacheMetadata(wfe);

                // named entity if an entity has been passed in as input?
                if (e.getEntityType().equalsIgnoreCase(entity))
                    oentity = wfe;
            }

            // Make sure we have the main entity definition
            if (oentity == null)
                throw new Exception("Entity (metadata) for " + entity + " not found");

            // If entity data is provided lets also get the generic entity for
            // that
            if (entityData != null && !entityData.isEmpty())
            {
                logInfo("execute", "Processing entitydata");

                LinkedHashMap<String, JsonNode> entityMap = mapper.readValue(entityData, new TypeReference<LinkedHashMap<String, JsonNode>>() {
                });
                for (Entry<String, JsonNode> entry : entityMap.entrySet())
                {
                    oentity.setValue(entry.getKey(), setValueByType(entry.getValue()));
                }
            }
            else if (opn != Operation.Select)
            {
                throw new Exception("Entity (entitydata) is a mandatory input for the " + operation + " operation");
            }

            // Check parameters
            if (parameters != null && !parameters.isBlank())
            {
                params = mapper.readValue(parameters, new TypeReference<HashMap<String, String>>() {});
            }

        }
        catch (Exception e)
        {
            logError("execute", "Error reading input: " + e.getMessage());
            error = "Error reading input: " + e.getMessage();
            e.printStackTrace();
        }

        if (error.isEmpty())
        {
            logInfo("execute", "Processed Input, preparing call");

            try
            {
                // All data input validated and enums initialized?
                logInfo("execute", "Reading and processing System properties: " + system);

                // Extract out the system props for this system
                Map<String, String> allSysProps = principal.getAllWfSystemProps();
                if (allSysProps != null)
                {
                    HashMap<String, String> currSysProps = new HashMap<String, String>();
                    String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_ODATA;

                    // Add the system name as an extra parameter so that it
                    // helps for the caching
                    currSysProps.put("WORKFLOW_SYS_NAME", system);

                    String urlPath = "";
                    for (Map.Entry<String, String> entry : allSysProps.entrySet())
                    {
                        if (entry.getKey().startsWith(system))
                        {
                            String key = entry.getKey().substring(system.length() + 1).toUpperCase();
                            String value = entry.getValue();
                            
                            // Replace value with environment variables
                            value = substituteEnvironmentVar(value);
                            switch (key)
                            {
                                case "SYSTEMTYPE":
                                    currSysProps.put("WORKFLOW_SYS_TYPE", value);
                                    break;
                                case "URL":
                                    try
                                    {
                                        URL url = new URL(value);
                                        currSysProps.put("SERVER", url.getHost());
                                        currSysProps.put("PORT", Integer.toString(url.getPort() != -1 ? url.getPort() : url.getDefaultPort()));
                                        currSysProps.put("PROTOCOL", url.getProtocol());
                                        urlPath = url.getPath();
                                        if (urlPath.endsWith("/"))
                                            urlPath = urlPath.substring(0, urlPath.length() - 1);
                                    }
                                    catch (Exception e)
                                    {
                                        error = "URL: " + value + "is invalid.  Error: " + e.getMessage();
                                        e.printStackTrace();
                                    }
                                    break;

                                case "SERVICEPATH":
                                    currSysProps.put("SERVICE_PATH", value);
                                    break;

                                case "SERVICETYPE":
                                    if (value.equalsIgnoreCase("SAP Gateway")) value = "SAP_GATEWAY";
                                    currSysProps.put("SERVICE_TYPE", value.toUpperCase());
                                    break;

                                case "AUTHENTICATIONTYPE":
                                    currSysProps.put("AUTHENTICATION_TYPE", value);
                                    break;

                                case "MAXCONNECTIONS":
                                    int poolSize = 10; // Default
                                    try
                                    {
                                        poolSize = Integer.parseInt(value);
                                        poolSize /= 2; // Halve it and
                                                       // distribute to
                                                       // sync/async
                                    }
                                    catch (Exception e)
                                    {
                                    }

                                    // Translate the connection pool size to
                                    // max_sync and async
                                    currSysProps.put("MAX_SYNC_CONNECTIONS", Integer.toString(poolSize));
                                    currSysProps.put("MAX_ASYNC_CONNECTIONS", Integer.toString(poolSize));
                                    break;

                                default:
                                    currSysProps.put(key, value);
                                    break;
                            }
                        }
                    }

                    // Set the system properties for this request submission
                    principal.setCurrentWfSystemProps(currSysProps);

                    logInfo("execute", "Processed request input, processing operation");

                    String[] expandTerms = null;
                    switch (opn)
                    {
                        case Select: // Set filter and fall through to read as
                                     // they are identical
                            odataRequest.setFilter(filter);
                        case Read:
                            expandTerms = expand != null ? expand.split(",") : null;
                            odataRequest.setExpand(expandTerms);
                            odataRequest.setParameters(params);
                            odataRequest.setEntity(oentity, opn);
                            break;
                        case Create:
                        case Update:
                        case Merge:
                        case Delete:
                            odataRequest.setEntity(oentity, opn);
                            break;
                    }

                    logInfo("execute", "Processed operation, executing call");

                    // Call
                    IODataResponse odataResponse = (IODataResponse) process.getService().submitRequest(odataRequest, functionName);

                    // If success return is the httpcode, all headers and the
                    // returned data
                    if (odataResponse.isSuccess())
                    {
                        logInfo("execute", "Execute successful, processing output");

                        if (!odataResponse.getEntity().isEmpty()) {
                            // The result will be returned as a JSON
                            JsonArray jArray = new JsonArray();
                            for (com.unvired.odata.meta.Entity e : odataResponse.getEntity())
                            {
                                logDebug("OData row: ", e.toString());
                                JsonObject jObj = new JsonObject();
                                for (com.unvired.odata.meta.Field field : e.getProperties())
                                {
                                    if (e.getValue(field.getName()) != null)
                                        setValueByType(jObj, field.getName(), e.getValue(field.getName()));
                                }
                                // Any navigations ?
                                for (Navigation n : e.getNavigationMeta())
                                {
                                    JsonArray jiArray = new JsonArray();
                                    List<com.unvired.odata.meta.Entity> navlist = e.getNavigation(n.getName());
                                    if (navlist != null)
                                    {
                                        for (com.unvired.odata.meta.Entity ne : navlist)
                                        {
                                            JsonObject jiObj = new JsonObject();
                                            for (com.unvired.odata.meta.Field ifield : ne.getProperties())
                                            {
                                                if (ne.getValue(ifield.getName()) != null)
                                                    setValueByType(jiObj, ifield.getName(), e.getValue(ifield.getName()));
                                            }
                                            jiArray.add(jiObj);
                                        }
                                        if (jiArray.size() > 0)
                                            jObj.add(n.getName(), jiArray);
                                    }
                                }
                                jArray.add(jObj);
                            }
    
                            dataRows = new Gson().toJson(jArray);
                        } else {                    // Should we treat no data found as an error?
                            if (errorMsgOnNoData != null && !errorMsgOnNoData.isEmpty())
                                error = errorMsgOnNoData;
                        }
                    }
                    else
                    {
                        // Get the error message and additionally any info
                        // messages.
                        process.getInfoMessageList().addAll(odataResponse.getInfoMessages());
                        List<InfoMessage> messages = odataResponse.getInfoMessages();
                        for (InfoMessage msg : messages)
                            if (msg.getCategory() == InfoMessageCategory.FAILURE)
                                error += msg.getMessage() + "\n";
                    }
                }
                else
                {
                    error = "OData System properties to connect to not provided";
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
                error = "System Error: " + e.getMessage();
            }
        }

        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
        {
            logInfo("execute", "Processing complete, returning results");
        }
        else
            logError("execute", error);

        return results;
    }

    private Object setValueByType(JsonNode value)
    {
        Object obj;
        if (value.isIntegralNumber())
            obj = value.asInt();
        else if (value.isFloatingPointNumber())
            obj = value.asDouble();
        else if (value.isBoolean())
            obj = value.asBoolean();
        else
            obj = value.asText();

        return obj;
    }

    private void setValueByType(JsonObject jobj, String key, Object value)
    {
        if (Boolean.class.isInstance(value))
            jobj.addProperty(key, (Boolean) value);
        else if (LocalDateTime.class.isInstance(value))
            jobj.addProperty(key, value.toString());
        else if (Number.class.isInstance(value))
            jobj.addProperty(key, (Number) value);
        else 
            jobj.addProperty(key, value != null ? value.toString() : "");
    }
}
