package com.unvired.operation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.BooleanNode;
import com.fasterxml.jackson.databind.node.NumericNode;
import com.fasterxml.jackson.databind.node.ValueNode;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;

public class MapFormSubmission extends AbstractWorkflowRequest
{
    @SuppressWarnings("unchecked")
    @Action(name = "mapformsubmission",
        outputs = 
        {
            @Output("mapformResult"),
            @Output("mapformError")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> transform(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "formdata", required = true) String data,
            @Param(value = "outputentitymap", required = false) String entityMap) 
    {
        Map<String, String> results = new HashMap<>();
        String error = "", transResult = "";
        
        Map<String, Object> dataMap = new LinkedHashMap<String, Object>();
        try
        {
            init(convId);
        
            // Load data into an in-memory LinkedHashMap to process in order
            // The recursive call loads the data into the map as actual values, or as an array or as another map
            // The inner map can result in similarly Array or values or a map
            logger.addInfoLog("MapFormSubmission", "transform", "Loading data");
            loadData(data, dataMap);

            Map<String, Map<String, String>> transformMap = new LinkedHashMap<String, Map<String, String>>();
            Map<String, EntityMappingInfo> mappingInfo = new LinkedHashMap<String, EntityMappingInfo>();
            
            // Load mapping into an in-memory LinkedHashMap to process in order
            // Additional mapping info of teh main maps and the fields to extract is maintained in the mappingInfo
            logger.addInfoLog("MapFormSubmission", "transform", "Loading entity mapping");
            loadMapping(entityMap, dataMap, transformMap, mappingInfo);

            // Start processing
            logger.addInfoLog("MapFormSubmission", "transform", "Creating output data");
            JsonObject result = new JsonObject();
            for (Map.Entry<String, Map<String, String>> entry : transformMap.entrySet())
            {
                Map<String, String> fields = entry.getValue();
                EntityMappingInfo mapInfo = mappingInfo.get(entry.getKey());

                // Header level or item level ?
                if (mapInfo.outerKey == null)
                {
                    JsonObject header = new JsonObject();
                    for (Map.Entry<String, String> entry2 : fields.entrySet())
                    {
                        String key = entry2.getKey();
                        Object value = dataMap.get(entry2.getValue());
                        assignValue(key, value, header);
                    }
                    result.add(entry.getKey(), header);
                }
                else
                {
                    // Run loop for outerkey
                    ArrayList<?> outer = (ArrayList<?>) dataMap.get(mapInfo.outerKey);
                    JsonArray array = new JsonArray();
                    for (Object obj: outer)
                    {
                        Map<String, Object> outerNode = (Map<String, Object>) obj;
                        
                        // Do we only have 1 level in?
                        if (mapInfo.innerKey == null)
                        {
                            JsonObject item = new JsonObject();
                            
                            // Loop over every entity field
                            for (Map.Entry<String, String> entry2 : fields.entrySet())
                            {
                                String key = entry2.getValue();
                                Object value = null;
                                if (key.startsWith(mapInfo.outerKey))
                                {
                                    String field =  key.substring(mapInfo.outerKey.length()+1);
                                    if (mapInfo.outerFields.contains(field))
                                        value = outerNode.get(field);
                                }
                                else
                                {
                                    value = dataMap.get(key);
                                }
                                key = entry2.getKey();              // Entity attribute name
                                assignValue(key, value, item);
                            }
                            array.add(item);
                        }
                        else            // Three level, need to run another loop
                        {
                            ArrayList<?> inner = (ArrayList<?>) outerNode.get(mapInfo.innerKey);
                            for (Object obj2: inner)
                            {
                                Map<String, Object> innerNode = (Map<String, Object>) obj2;
                                JsonObject item = new JsonObject();
                                
                                // Loop over every entity field
                                for (Map.Entry<String, String> entry2 : fields.entrySet())
                                {
                                    String key = entry2.getValue();
                                    Object value = null;
                                    if (key.startsWith(mapInfo.outerKey + "." + mapInfo.innerKey))
                                    {
                                        String field =  key.substring(mapInfo.outerKey.length()+1+mapInfo.innerKey.length()+1);
                                        if (mapInfo.innerFields.contains(field))
                                            value = innerNode.get(field);
                                    }
                                    else if (key.startsWith(mapInfo.outerKey))
                                    {
                                        String field =  key.substring(mapInfo.outerKey.length()+1);
                                        if (mapInfo.outerFields.contains(field))
                                            value = outerNode.get(field);
                                    }
                                    else
                                    {
                                        value = dataMap.get(key);
                                    }
                                    key = entry2.getKey();              // Entity attribute name
                                    assignValue(key, value, item);
                                }
                                array.add(item);
                            }
                        }
                    }
                    result.add(entry.getKey(), array);                            
                }
            }
            logger.addInfoLog("MapFormSubmission", "transform", "Output data created");
            transResult = new Gson().toJson(result);
        }
        catch (Exception e)
        {
            error = "System Error: " + e.getMessage();
            e.printStackTrace();
        }
        
        results.put("mapformResult", transResult);
        results.put("mapformError", error);
        
        if (error.isEmpty())
            logger.addInfoLog("MapFormSubmission", "transform", "Processing complete, returning results/error");
        else
            logger.addErrorLog("MapFormSubmission", "transform", error);
        
        return  results;
    }

    @SuppressWarnings("unchecked")
    private void loadData(String input, Object mapOrList) throws Exception
    {
        logger.addInfoLog("MapFormSubmission", "loadData", "Start");
        
        // Input can be an ArrayList or a Map to fill
        Map<String, Object> dataMap = null;
        List<Object> dataList = null;
        if (mapOrList instanceof Map<?,?>)
            dataMap = (Map<String, Object>) mapOrList;
        else
            dataList = (ArrayList<Object>) mapOrList;
        
        // Load data into an in-memory LinkedHashMap to process in order
        ObjectMapper mapper = new ObjectMapper();
        if (dataMap != null)                // Are we handling a map?
        {
            Map<String, JsonNode> map = mapper.readValue(input, new TypeReference<LinkedHashMap<String, JsonNode>>() {});
            for (Map.Entry<String, JsonNode> dataEntry : map.entrySet()) 
            {
                if (dataEntry.getValue().isObject())
                {
                    Map<String, Object> map2 = new LinkedHashMap<String, Object>();
                    logger.addInfoLog("MapFormSubmission", "loadData", "Recursing over Map->JSON Object");
                    loadData(mapper.writeValueAsString(dataEntry.getValue()), map2);
                    dataMap.put(dataEntry.getKey(), map2);
                }
                else if (dataEntry.getValue().isArray())
                {
                    ArrayList<Object> array = new ArrayList<Object>();
                    logger.addInfoLog("MapFormSubmission", "loadData", "Recursing over Map->JSON Array");
                    loadData(mapper.writeValueAsString(dataEntry.getValue()), array);
                    dataMap.put(dataEntry.getKey(), array);
                }
                else
                {
                    dataMap.put(dataEntry.getKey(), dataEntry.getValue());
                }
            }
        }
        else
        {
            List<JsonNode> list = mapper.readValue(input, new TypeReference<ArrayList<JsonNode>>() {});
            for (JsonNode dataEntry: list)
            {
                if (dataEntry.isObject())
                {
                    Map<String, Object> map = new LinkedHashMap<String, Object>();
                    logger.addInfoLog("MapFormSubmission", "loadData", "Recursing over Array->JSON Object");
                    loadData(mapper.writeValueAsString(dataEntry), map);
                    dataList.add(map);
                }
                else if (dataEntry.isArray())
                {
                    ArrayList<Object> array = new ArrayList<Object>();
                    logger.addInfoLog("MapFormSubmission", "loadData", "Recursing over Array->JSON Array");
                    loadData(mapper.writeValueAsString(dataEntry), array);
                    dataList.add(array);
                }
                else
                {
                    dataList.add(dataEntry);
                }
            }
        }
        logger.addInfoLog("MapFormSubmission", "loadData", "End");
    }
    
    private void loadMapping(String entityMap, Map<String, Object> dataMap, Map<String, Map<String, String>> transformMap, Map<String, EntityMappingInfo> mappingInfo) throws Exception
    {
        logger.addDebugLog("MapFormSubmission", "loadMapping", "Start");
        
        // Read the transform mapping
        ObjectMapper mapper = new ObjectMapper();

        // Entity keys map
        Map<String, JsonNode> map = mapper.readValue(entityMap, new TypeReference<LinkedHashMap<String, JsonNode>>() {});
        logger.addDebugLog("MapFormSubmission", "loadMapping", "Iterating over entity map");
        for (Map.Entry<String, JsonNode> dataEntry : map.entrySet()) 
        {
            EntityMappingInfo mapInfo = new EntityMappingInfo();
            if (dataEntry.getValue().isContainerNode())
            {
                logger.addDebugLog("MapFormSubmission", "loadMapping", "Iterating over all fields");
                
                // Fields map for the entity key
                Map<String, String> innerMap =  mapper.readValue(dataEntry.getValue().toString(), new TypeReference<LinkedHashMap<String, String>>() {});
                for (Map.Entry<String, String> dataEntry2 : innerMap.entrySet())
                {
                    String entityKey = dataEntry2.getValue();
                    String[] dotParts = entityKey.split("\\.");
                    
                    // Check if found in data?
                    Object dataObj = dataMap.get(dotParts[0]);
                    if (dataObj == null)
                        throw (new Exception("Invalid entity map. Field: " + dotParts[0] + " not found"));

                    // Second level has to be an array
                    if (dotParts.length > 1 && !(dataObj instanceof ArrayList<?>))
                        throw (new Exception("Invalid entity map: " + dotParts[0] + " not an array, unable to extract: " + entityKey));
                    
                    if (dotParts.length == 2)
                    {
                        // 2 level, first key is an array
                        if (mapInfo.outerKey == null)
                            mapInfo.outerKey = dotParts[0];
                        
                        // For 2 level nesting like a.b, only one array a is allowed
                        if (mapInfo.outerKey.equalsIgnoreCase(dotParts[0]))
                        {
                            mapInfo.outerFields.add(dotParts[1]);
                        }
                        else
                        {
                            throw (new Exception("Invalid entity map.  Mapping of only 1 array item type allowed: " + mapInfo.outerKey + " already mapped, " + dotParts[0] + " not allowed"));
                        }
                    }
                    else if (dotParts.length == 3)
                    {
                        // For 3 level nesting like a.b.c, a.b must already be processed
                        if (!mapInfo.outerKey.equalsIgnoreCase(dotParts[0]))
                            throw (new Exception("Invalid entity map.  Mapping of only 1 array item type allowed: " + mapInfo.outerKey + " already mapped, " + dotParts[0] + " not allowed"));
                        
                        // Get the first array item and extract the object with the
                        if (((ArrayList<?>) dataObj).isEmpty())
                            continue;
                        
                        Object dataObj2 = ((ArrayList<?>) dataObj).get(0);
                        if (dataObj2 instanceof Map<?, ?>)
                        {
                            Object dataObj3 = ((Map<?, ?>) dataObj2).get(dotParts[1]);
                            if (dataObj3 instanceof ArrayList<?>)
                            {
                                if (mapInfo.innerKey == null)
                                    mapInfo.innerKey = dotParts[1];
                                
                                // For 3 level nesting like a.b.c, only one inner array b is allowed
                                if (mapInfo.innerKey.equalsIgnoreCase(dotParts[1]))
                                {
                                    mapInfo.innerFields.add(dotParts[2]);
                                }
                                else
                                {
                                    throw (new Exception("Invalid entity map.  Mapping of only 1 array item type allowed: " + mapInfo.innerKey + " already mapped, " + dotParts[1] + " not allowed"));
                                }
                            }
                            else
                            {
                                throw (new Exception("Invalid entity map: " + dotParts[1] + " not an array, unable to extract: " + entityKey));
                            }
                        }
                        else
                        {
                            throw (new Exception("Invalid entity map, array: " + dotParts[0] + " does not contain objects, unable to extract: " + entityKey));
                        }
                    }
                    else if (dotParts.length > 3)
                        throw (new Exception("Invalid entity map.  Mapping of only 3 levels supported, use custom mapper instead"));
                }
                
                // All fine, add for processing
                logger.addDebugLog("MapFormSubmission", "loadMapping", "Adding mapInfo and fields map: " + mapInfo.toString());
                mappingInfo.put(dataEntry.getKey(), mapInfo);
                transformMap.put(dataEntry.getKey(), innerMap);
            }
            else
            {
                throw (new Exception("Invalid entity map.  Has to be a JSON of entity objects"));
            }
        }
        logger.addDebugLog("MapFormSubmission", "loadMapping", "End");
    }
 
    class EntityMappingInfo
    {
        String outerKey = null;
        String innerKey = null;
        ArrayList<String> outerFields = new ArrayList<String>();
        ArrayList<String> innerFields = new ArrayList<String>();
        
        public String toString()
        {
            StringBuilder sb = new StringBuilder();
            sb.append("outerKey: ");
            if (outerKey != null)
            {
                sb.append(outerKey);
                sb.append(" with fields: " + outerFields.toString());
            }
            else
                sb.append("Not set");

            sb.append(" and innerKey: ");
            if (innerKey != null)
            {
                sb.append(innerKey);
                sb.append(" with fields: " + innerFields.toString());
            }
            else
                sb.append("Not set");
            
            return sb.toString();
        }
    }
    
    // Check type and assign a value.  This ensures that a number for e.g. will be a number in the JSON and not always a string
    private void assignValue(String key, Object value, JsonObject node)
    {
        if (value instanceof NumericNode)
            node.addProperty(key, ((NumericNode) value).numberValue());
        else if (value instanceof BooleanNode)
            node.addProperty(key, ((BooleanNode) value).asBoolean());
        else
            node.addProperty(key, ((ValueNode) value).asText());        
    }
}