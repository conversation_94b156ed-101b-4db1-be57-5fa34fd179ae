package com.unvired.operation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.jdbc.meta.ColumnMeta;
import com.unvired.jdbc.proxy.Row;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.IJDBCRequest;
import com.unvired.ump.agent.IJDBCResponse;

public class DBRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_DB_PART1 = "ADHOC_";
    private static final String ADHOC_FUNCTION_DB_PART2 = "_DB_JDBC_EXECUTE";
    private static final int NUM_RECORDS = 100;
    
    @Action(name = "query",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> query(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "system", required = true) String system,
            @Param(value = "query", required = false) String query,
            @Param(value = "operation", required = false) String operation,
            @Param(value = "keys", required = false) String keys,
            @Param(value = "data", required = false) String data,
            @Param(value = "flattenoutput", required = false) String flatten,
            @Param(value = "errormessageonnodata", required = false) String errorMsgOnNoData) 
    {
        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";

        try
        {
            init(convId);
            
            logInfo("query", "Preparing call");
            logDebug("query", "query: " + (String) (query != null ? query : ""));
            logDebug("query", "operation: " + (String) (operation != null ? operation : ""));
            logDebug("query", "keys: " + (String) (keys != null ? keys : ""));
            // logDebug("query", "data: " + (String) (data != null ? data : ""));
            logDebug("query", "flattenoutput: " + (String) (flatten != null ? flatten : ""));
            logDebug("query", "errormessageonnodata: " + (String) (errorMsgOnNoData != null ? errorMsgOnNoData : ""));
            
            
            logInfo("query", "Processing System Properties");
            Boolean multiQuery = false;
            Boolean flattenData = Boolean.parseBoolean(flatten);
            String dbType = "";
            
            // Extract out the system props for this system
            Map<String, String> allSysProps = principal.getAllWfSystemProps();
            if (allSysProps != null)
            {
                HashMap<String, String> currSysProps = new HashMap<String, String>();
                String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_DB_PART1;
                
                // Add the system name as an extra parameter so that it helps for the caching
                currSysProps.put("WORKFLOW_SYS_NAME", system);
                
                for (Map.Entry<String, String> entry : allSysProps.entrySet())
                {
                    if (entry.getKey().startsWith(system))
                    {
                        String key = entry.getKey().substring(system.length()+1).toUpperCase();
                        String value = entry.getValue();
                        
                        // Replace value with environment variables
                        value = substituteEnvironmentVar(value);
                        if (key.equals("SYSTEMTYPE"))
                        {
                            currSysProps.put("WORKFLOW_SYS_TYPE", value);
                        }
                        else if (key.equals("DBTYPE"))
                        {
                            // No need to add this key to list .. just to set the function name
                            functionName += value.toUpperCase() + ADHOC_FUNCTION_DB_PART2;
                            dbType = value.toUpperCase();
                        }
                        else if (key.equals("MULTIQUERY"))
                        {
                            multiQuery = Boolean.parseBoolean(value);
                        }
                        else if (key.equals("CONNECTIONPOOL"))
                        {
                            int poolSize = 10;      // Default
                            try
                            {
                                poolSize = Integer.parseInt(value);
                                poolSize /= 2;          // Halve it and distribute to sync/async
                            }
                            catch(Exception e) {}
                            
                            // Translate the connection pool size to max_sync and async
                            currSysProps.put("MAX_SYNC_CONNECTIONS", Integer.toString(poolSize));
                            currSysProps.put("MAX_ASYNC_CONNECTIONS", Integer.toString(poolSize));
                        }
                        else
                            currSysProps.put(key, value);
                    }
                }
                
                // Check if teh function is set correctly
                if (functionName.equals(application.toUpperCase() + "_" + ADHOC_FUNCTION_DB_PART1))
                {
                    throw new Exception("Database type needs to be defined in system properties file for the system: " + system);
                }
                
                // If connection pooling is not specified (sync and async are not added) then we need to add the CLIENT_CREDENTIALS value so that connection pooling is disabled
                if (!currSysProps.containsKey("MAX_SYNC_CONNECTIONS"))
                    currSysProps.put("USE_CLIENT_CREDENTIALS", "YES");
                
                // Handle the connection parameters as UMP expects "CONNECTION_PARAMETERS" but we accept "connectionParameters" in the property file to avoid confusion
                String connParam = currSysProps.get("CONNECTIONPARAMETERS");
                if (connParam != null)
                    currSysProps.put("CONNECTION_PARAMETERS", connParam);
                
                // Set the system properties for this request submission
                principal.setCurrentWfSystemProps(currSysProps);
                
                // Get the native sql statement.  ArrayList as depending on configuration we will execute as a single statement or
                // multiple statements
                ArrayList<String> statements = null;
                if (query != null)          // Native query provided
                {
                    statements = new ArrayList<String>();
                    if (multiQuery)
                    {
                        String[] queries = query.split(";");
                        for (String snglquery: queries)
                            statements.add(snglquery + ";");
                    }
                    else
                        statements.add(query);
                }
                else
                {
                    // Is this an upsert operation?  currently only supported in MySQL
                    if ("upsert".equalsIgnoreCase(operation) && !dbType.equalsIgnoreCase("MYSQL")) {
                        throw new Exception("upsert is only supported on MySQL currently");
                    }
                    
                    statements = getNativeSQL(dbType, operation, keys, data);
                }
                
                if (statements == null || statements.isEmpty())
                    throw new Exception("Either query or operation needs to be specified");
                
                logDebug("query", "Statements: " + statements.toString());
                
                Iterator<List<Row>> iterator = null;
                com.unvired.jdbc.proxy.NativeSQL nativeSQLOutput = null;
                IJDBCRequest jdbcRequest = (IJDBCRequest) process.createRequest(RequestType.JDBC);
                jdbcRequest.setRequestType(IJDBCRequest.Mode.Native);
                
                logInfo("query", "Initiating call");
                
                IJDBCRequest jdbcTrans = (IJDBCRequest) process.createRequest(RequestType.JDBC);
                jdbcTrans.setRequestType(IJDBCRequest.Mode.StartTransaction);
                process.getService().submitRequest(jdbcTrans, functionName);

                //  Call
                IJDBCResponse jdbcResponse = null;
                boolean tranError = false;
                String currStatement = null;
                try
                {
                    for (String statement: statements)
                    {
                        currStatement = statement;          // In case of exception need this to print the SQL statement
                        com.unvired.jdbc.proxy.NativeSQL nativeSQLInput = new com.unvired.jdbc.proxy.NativeSQL(statement); 
                        jdbcRequest.setNativeSQL(nativeSQLInput);                
                        jdbcResponse = (IJDBCResponse) process.getService().submitRequest(jdbcRequest, functionName);
                        if (!jdbcResponse.isSuccess())
                        {
                            tranError = true;
                            break;
                        }
                    }
                }
                catch (Exception e)
                {
                    tranError = true;
                    e.printStackTrace();
                    
                    logError("query", "Statements executed: " + statements.toString());
                    logError("query", "--------------------- The Error Statement -------------------------------------");
                    logError("query", "Statement causing the error: " + currStatement);
                }
                finally
                {
                    if (!tranError)
                        jdbcTrans.setRequestType(IJDBCRequest.Mode.Commit);
                    else
                        jdbcTrans.setRequestType(IJDBCRequest.Mode.Rollback);
                    process.getService().submitRequest(jdbcTrans, functionName);
                }
    
                if (jdbcResponse.isSuccess()) 
                {
                    if (error.isEmpty())            // This is valid only for DB Queries and not data based calls
                    {
                        logInfo("query", "Processing complete, extracting data rows");
                        
                        nativeSQLOutput = jdbcResponse.getNativeSQLOutput();
                        iterator = nativeSQLOutput.getResultSets();
                        if (iterator != null && iterator.hasNext()) 
                        {
                            List<Row> rows = iterator.next();
                            if (rows != null && !rows.isEmpty())
                            {
                                ArrayList<String> jsonFields = new ArrayList<String>();
                                HashMap<String, String> fieldNamesMap = new HashMap<String, String>();
                                HashMap<String, Integer> nameToIndex = new HashMap<String, Integer>();
                                JsonArray jArray = new JsonArray();
                                for (Row row : rows) 
                                {
                                    // Get the columnName to index mapping and then reuse across rows
                                    if (nameToIndex.isEmpty())
                                    {
                                        logInfo("query", "Processing column meta");
                                        
                                        // Set fieldmap it to all the columns that have been retrieved
                                        List<ColumnMeta> columns = row.getMetaData().getColumns();
                                        for (ColumnMeta column: columns)
                                        {
                                            logDebug("query", "*** Column Meta: " + column.getDescription() + " : " + (column.getUdtType() != null ? column.getUdtType() : ""));
                                            nameToIndex.put(column.getDescription(), column.getIndex());
                                            fieldNamesMap.put(column.getDescription(), column.getDescription());
                                            if (column.getUdtType() != null && column.getUdtType().toLowerCase().contains("json")) {
                                                logInfo("query", "**** Marking JSON field:" + column.getDescription());
                                                jsonFields.add(column.getDescription());
                                            }
                                        }
                                    }
                                    
                                    // Get all the required fields and add them
                                    JsonObject oneRow = new JsonObject();
                                    for (Map.Entry<String, String> entry : fieldNamesMap.entrySet())
                                    {
                                        Object value = row.getValue(nameToIndex.get(entry.getKey()));
                                        // Add null value if db column is null
                                        if (value == null) {
                                            oneRow.add(entry.getValue(), JsonNull.INSTANCE);
                                        }
                                        else if (jsonFields.contains(entry.getKey())) {
                                            try {
                                                logDebug("query", "**** Adding JSON field:" + "" + value);
                                                JsonElement elem = new JsonParser().parse("" + value);
                                                oneRow.add(entry.getValue(), elem);
                                            } catch (Exception e) {         // Some JSON parse error, add as string
                                                oneRow.addProperty(entry.getValue(), "" + value);
                                            }
                                        }
                                        else if (value instanceof Number)
                                            oneRow.addProperty(entry.getValue(), (Number) value);
                                        else if (value instanceof Boolean)
                                            oneRow.addProperty(entry.getValue(), (Boolean) value);
                                        else if (value instanceof Character)
                                            oneRow.addProperty(entry.getValue(), (Character) value);
                                        else
                                            oneRow.addProperty(entry.getValue(), "" + value);
                                    }
                                    jArray.add(oneRow);
                                }
                                
                                GsonBuilder builder = new GsonBuilder(); 
                                builder.serializeNulls(); 
                                Gson gson = builder.create(); 
                                
                                if (jArray.size() == 1 && flattenData)
                                    dataRows = gson.toJson(jArray.get(0)); 
                                else
                                    dataRows = gson.toJson(jArray);
                            } else {                    // Should we treat no data found as an error?
                                if (errorMsgOnNoData != null && !errorMsgOnNoData.isEmpty())
                                    error = errorMsgOnNoData;
                                else
                                    dataRows = flattenData ? "{}" : "[]";
                            }
                        }
                        else {                    // Should we treat no data found as an error?
                            if (errorMsgOnNoData != null && !errorMsgOnNoData.isEmpty())
                                error = errorMsgOnNoData;
                            else
                                dataRows = flattenData ? "{}" : "[]";
                        }
                    }
                } 
                else
                {
                    process.getInfoMessageList().addAll(jdbcResponse.getInfoMessages());
                    List<InfoMessage> messages = jdbcResponse.getInfoMessages();
                    for (InfoMessage msg: messages)
                        if (msg.getCategory() == InfoMessageCategory.FAILURE)
                            error += msg.getMessage() + "\n"; 
                }
            }
            else
            {
                error = "Database System properties to connect to not provided";
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = e.getMessage();
        }

        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("query", "Processing complete, returning results");
        else
            logError("query", error);
        
        return  results;
    }

    @SuppressWarnings("unchecked")
    private ArrayList<String> getNativeSQL(String dbType, String operation, String keys, String data) throws Exception
    {
        if (data == null)
            throw new Exception("Either query or data should be specified for execution");
        
        
        ObjectMapper mapper = new ObjectMapper();
        
        if (keys == null && (operation.equalsIgnoreCase("update") || operation.equalsIgnoreCase("delete")))
            throw new Exception("key attribute not specified in operation input");
        
        Map<String, Object> keyMap = null;
        if (keys != null)
            keyMap = mapper.readValue(keys, new TypeReference<LinkedHashMap<String, Object>>() {});
        
        // Get all the entities passed in .. this is the output of the transformresult operation
        ArrayList<String> statements = new ArrayList<String>();
        Map<String, Object> dataRows = mapper.readValue(data, new TypeReference<LinkedHashMap<String, Object>>() {});
        
        if (dataRows.isEmpty())
            throw new Exception("No data available to process");
        
        for (Map.Entry<String, Object> dataEntry : dataRows.entrySet()) 
        {
            if (dataEntry.getValue() instanceof ArrayList<?>)
            {
                ArrayList<Map<String,String>> array = (ArrayList<Map<String,String>>) dataEntry.getValue();
                if (operation.equalsIgnoreCase("insert") || operation.equalsIgnoreCase("upsert"))
                {
                    String stmt = generateStmt(dbType, operation, dataEntry.getKey(),  array, keyMap != null ? keyMap.get(dataEntry.getKey()) : null, "~");
                    String[] queries = stmt.split("~");
                    for (String snglquery: queries)
                        statements.add(snglquery);
                }
                else
                {
                    for (Map<String,String> node: array)
                        statements.add(generateStmt(dbType, operation, dataEntry.getKey(),  node, keyMap != null ? keyMap.get(dataEntry.getKey()) : null, null));
                }
            }
            else
                statements.add(generateStmt(dbType, operation, dataEntry.getKey(), (Map<String,String>) dataEntry.getValue(), keyMap != null ? keyMap.get(dataEntry.getKey()) : null, null));
        }
        
        return statements;
    }
    
    @SuppressWarnings("unchecked")
    private String generateStmt(String dbType, String opn, String table, Object columns, Object keys, String delimiter) throws Exception
    {
        switch (opn)
        {
            case "insert":
            case "upsert":
                ArrayList<Map<String, String>> rows = null;
                if (columns instanceof Map<?,?>)
                {
                    rows = new ArrayList<Map<String, String>>();
                    rows.add((Map<String, String>) columns);
                }
                else
                {
                    rows = (ArrayList<Map<String, String>>) columns;
                }
                if (rows.size() > NUM_RECORDS) {
                    int balance = rows.size() % NUM_RECORDS;
                    int startIndex = 0;
                    StringBuilder bldr = new StringBuilder();
                    while (true) {
                        bldr.append(generateInsertStmt(dbType, table, rows.subList(startIndex, startIndex+NUM_RECORDS), opn));
                        if (delimiter != null)
                            bldr.append(delimiter);
                        
                        startIndex += NUM_RECORDS;
                        
                        // Are we at the end?
                        if ((startIndex + balance) == rows.size()) {
                            if (balance > 0)       // If a multiple if NUM_RECORDS we are already done
                                bldr.append(generateInsertStmt(dbType, table, rows.subList(startIndex, startIndex+balance), opn));
                            break;
                        }
                    }
                    return bldr.toString();
                }
                // Will fit in a single call
                return generateInsertStmt(dbType, table, rows, opn);
                
            case "update":
                return generateUpdateStmt(dbType, table, (Map<String, String>) columns, keys);
                
            case "delete":
                return generateDeleteStmt(dbType, table, (Map<String, String>) columns, keys);
        }
        
        return null;
    }
    
    private String generateInsertStmt(String dbType, String table, List<Map<String,String>> rows, String opn) throws Exception
    {
        boolean mysql = dbType.equalsIgnoreCase("MYSQL");
        boolean pgsql = dbType.equalsIgnoreCase("PGSQL");
        StringBuilder sb = new StringBuilder(), sbvalues = new StringBuilder(), sbUpsert = null;
        
        // If its an insert or update
        if ("upsert".equalsIgnoreCase(opn)) {
            sbUpsert = new StringBuilder();
            sbUpsert.append(" ON DUPLICATE KEY UPDATE ");
        }
        
        // For pgsql the table and column names are to be quoted to make it case sensitive
        if (pgsql)
            sb.append("INSERT INTO ").append("\"").append(table).append("\" (");
        else
            sb.append("INSERT INTO ").append(table).append(" (");
        sbvalues.append("VALUES ");

        // Process each row
        boolean stmtCreate = true;
        for (Map<String,String> row: rows)
        {
            sbvalues.append("(");
            for (Map.Entry<String, String> entry : row.entrySet()) 
            {
                if (stmtCreate) {
                    if (pgsql)
                        sb.append("\"").append(entry.getKey()).append("\", ");
                    else
                        sb.append(entry.getKey()).append(", ");
                    if (sbUpsert != null)
                        sbUpsert.append(entry.getKey()).append("=VALUES(").append(entry.getKey()).append("), ");
                }
                sbvalues.append(valueAsString(mysql, entry.getValue())).append(", ");
            }
            if (stmtCreate)
            {
                sb.replace(sb.length()-2, sb.length(), ") ");
                if (sbUpsert != null)
                    sbUpsert.replace(sbUpsert.length()-2, sbUpsert.length(), ";");
                
                stmtCreate = false;
            }
            sbvalues.replace(sbvalues.length()-2, sbvalues.length(), "), ");
        }
        if (sbUpsert == null)
            sbvalues.replace(sbvalues.length()-2, sbvalues.length(), ";");
        else
            sbvalues.replace(sbvalues.length()-2, sbvalues.length(), "");
        sb.append(sbvalues);
        
        // If its an upsert add the update bit
        if (sbUpsert != null)
            sb.append(sbUpsert);
        
        return sb.toString();
    }
    
    private String generateUpdateStmt(String dbType, String table, Map<String, String> columns, Object keys)  throws Exception
    {
        boolean mysql = dbType.equalsIgnoreCase("MYSQL");
        StringBuilder sb = new StringBuilder();
        sb.append("update ").append(table).append(" set ");
        
        ArrayList<String> whereColumns = makeArray(keys);
        for (Map.Entry<String, String> entry : columns.entrySet()) 
        {
            if (!whereColumns.contains(entry.getKey()))
                sb.append(entry.getKey()).append("=").append(valueAsString(mysql, entry.getValue())).append(", ");
        }
        sb.replace(sb.length()-2, sb.length(), " where ");
        
        for (String key: whereColumns)
            sb.append(key).append("=").append(valueAsString(mysql, columns.get(key))).append(" and ");
        
        sb.replace(sb.length()-5, sb.length(), ";");
        return sb.toString();
    }

    private String generateDeleteStmt(String dbType, String table, Map<String, String> columns, Object keys)  throws Exception
    {
        boolean mysql = dbType.equalsIgnoreCase("MYSQL");
        StringBuilder sb = new StringBuilder();
        sb.append("delete from ").append(table).append(" where ");
        
        ArrayList<String> whereColumns = makeArray(keys);
        for (String key: whereColumns)
            sb.append(key).append("=").append(valueAsString(mysql, columns.get(key))).append(" and ");
        
        sb.replace(sb.length()-5, sb.length(), ";");
        return sb.toString();    
    }
    
    @SuppressWarnings("unchecked")
    private ArrayList<String> makeArray(Object keys)  throws Exception
    {
        ArrayList<String> array = null;
        if (keys instanceof ArrayList<?>)
        {
            array = (ArrayList<String>) keys;
        }
        else
        {
            array = new ArrayList<String>();
            array.add((String) keys);
        }
        
        return array;
    }
    
    private String valueAsString(boolean mysql, Object value) throws Exception
    {
        String valueStr = "";
        // Since this is DB we treat the value "null" and "NULL" as NULL
        if (value == null)
            valueStr = "NULL";
        else if (value instanceof Number)
            valueStr = ((Number) value).toString();
        else if (value instanceof Boolean)
            valueStr = ((Boolean) value).toString();
        else if (value instanceof Character)
            valueStr = "'" + ((Character) value).toString().replace("'", "''") + "'";
        else if (value instanceof String)
            valueStr = "'" + ((String) value).replace("'", "''") + "'";
        else {
            valueStr = "" + value;
            
            // If JSON escape specially
            if ((valueStr.startsWith("{") || valueStr.startsWith("[")) && (valueStr.endsWith("}") || valueStr.endsWith("]")))
                valueStr = valueStr.replace("'", "\\\\'");           // Escape ' inside json
            
            valueStr = "'" + value + "'";
        }
        
        if (mysql) valueStr = valueStr.replace("\\", "\\\\");
        return valueStr;
    }    
}
