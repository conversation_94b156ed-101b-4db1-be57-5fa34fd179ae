package com.unvired.operation;

import java.sql.Date;
import java.sql.Time;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.Vector;
import org.joda.time.LocalDateTime;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.jco.Structure;
import com.unvired.jco.meta.DataElementMeta;
import com.unvired.jco.meta.FieldMeta;
import com.unvired.jco.meta.ParameterMeta;
import com.unvired.jco.meta.TypeMeta;
import com.unvired.jco.workflow.WFRFCFunction;
import com.unvired.jco.workflow.WFRFCStructure;
import com.unvired.jco.workflow.WFRFCTable;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.ISAPRFCRequest;
import com.unvired.ump.agent.ISAPRFCResponse;

public class SAPRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_SAP = "ADHOC_SAP_PORT_SAP_EXECUTE";
    protected Vector<InfoMessage> infoMessages;

    @Action(name = "sap", outputs = { @Output("result"), @Output("error") }, responses = { @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL), @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true) })

    public Map<String, String> execute(@Param(value = "alias", required = true) String alias, 
            @Param(value = "namespace", required = true) String namespace, 
            @Param(value = "application", required = true) String application, 
            @Param(value = "convId", required = true) String convId, 
            @Param(value = "system", required = true) String system, 
            @Param(value = "operation", required = true) String operation,
            @Param(value = "function", required = false) String function, 
            @Param(value = "transaction", required = false) String transaction,
            @Param(value = "parameters", required = false) String parameters, 
            @Param(value = "data", required = false) String data)
    {

        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";
        JsonNode rfcOutputs = null;
        ISAPRFCRequest rfcRequest = null;
        WFRFCFunction rfcFunc = null;
        
        // Parse the inputs json for the OData entities
        try
        {
            init(convId);

            logInfo("execute", "Processing Input");
            logDebug("execute", "system: " + system);
            logDebug("execute", "operation: " + operation);

            ObjectMapper mapper = new ObjectMapper();
            rfcFunc = new WFRFCFunction(function);
            
            // If these are session we dont need to do the parameters bit
            if (operation.equalsIgnoreCase("Execute")) {
                
                if (function == null || function.isEmpty() || parameters == null || parameters.isEmpty() || data == null || data.isEmpty()) {
                    logError("execute", "Mandatory parameters missing");
                    throw new Exception("Execute operation: function, parameters and data are required");
                }
                
                logDebug("execute", "function: " + function);
                logDebug("execute", "parameters: " + parameters);
                logDebug("execute", "data: " + data);
                logDebug("execute", "transaction: " + (String) (transaction != null ? transaction : ""));
                
                // Check parameters and create the required structures
                logInfo("execute", "Processing parameters");
                JsonNode root = mapper.readTree(parameters);
                JsonNode rfcInputs = root.findPath("RFCInput");
                rfcOutputs = root.findPath("RFCOutput");
                JsonNode rfcParams = root.findPath("RFCParameter");
                for (JsonNode param: rfcParams) {
                    // SAP Structure or Table
                    int type = param.get("TYPE").asInt();
                    if (type == 17 || type == 99) {
                        // Table to be created if type 99, structure will be created always
                        WFRFCTable table = type == 99 ? new WFRFCTable(param.get("SAP_TYPE").asText(), param.get("DESC").asText()) : null;
                        if (table != null) {
                            rfcFunc.cacheTable(param.get("SAP_TYPE").asText(), table);
                        }
                        
                        WFRFCStructure struct = new WFRFCStructure(param.get("SAP_TYPE").asText(), param.get("DESC").asText());
                        rfcFunc.cacheStructure(param.get("SAP_TYPE").asText(), struct);
                        
                        JsonNode fields = param.get("RFCField");
                        for (JsonNode field: fields) {
                            DataElementMeta elemMeta = new DataElementMeta(field.get("TYPE").asInt(), field.get("SAP_TYPE").asText(), 
                                            field.get("DESC").asText(), field.get("LENGTH").asInt(), field.get("DECIMAL").asInt());
                            struct.addFieldMeta(new FieldMeta(field.get("INDEX").asInt(), field.get("NAME").asText(), elemMeta));
                            if (table != null)
                                table.addFieldMeta(new FieldMeta(field.get("INDEX").asInt(), field.get("NAME").asText(), elemMeta));
                        }
                        
                        // Add the table meta or structure meta to the Function Descriptor
                        rfcFunc.addFuncDescriptorMeta(new ParameterMeta(param.get("INDEX").asInt(), param.get("NAME").asText(),
                                    param.get("DESC").asText(), table != null ? table.getMetaData() : struct.getMetaData(),
                                    param.get("DIRECTION").asInt()));
                        
                    } else {
                        DataElementMeta elemMeta = new DataElementMeta(param.get("TYPE").asInt(), param.get("NAME").asText(), 
                                            param.get("DESC").asText(), param.get("LENGTH").asInt(), param.get("DECIMAL").asInt());
                        rfcFunc.addFuncDescriptorMeta(new ParameterMeta(param.get("INDEX").asInt(), param.get("NAME").asText(), 
                                            param.get("DESC").asText(), elemMeta, param.get("DIRECTION").asInt()));
                    }
                }
    
                logInfo("execute", "Processing input data");
                processInput(mapper, rfcFunc, rfcInputs, data);
            }
        }
        catch (Exception e)
        {
            logError("execute", "Error reading input: " + e.getMessage());
            error = "Error reading input: " + e.getMessage();
            e.printStackTrace();
        }

        // Either no error while processing input or session related
        if (error.isEmpty())
        {
            logInfo("execute", "Processed Input, preparing call");

            try
            {
                // All data input validated and enums initialized?
                logInfo("execute", "Reading and processing System properties: " + system);

                // Extract out the system props for this system
                Map<String, String> allSysProps = principal.getAllWfSystemProps();
                if (allSysProps != null)
                {
                    HashMap<String, String> currSysProps = new HashMap<String, String>();
                    String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_SAP;

                    // Add the system name as an extra parameter so that it helps for the caching
                    currSysProps.put("WORKFLOW_SYS_NAME", system);
                    currSysProps.put("BAPI_NAME", function);
                    currSysProps.put("TRANSACTION_HANDLING", transaction != null ? transaction : "No");

                    for (Map.Entry<String, String> entry : allSysProps.entrySet())
                    {
                        if (entry.getKey().startsWith(system))
                        {
                            String key = entry.getKey().substring(system.length() + 1).toUpperCase();
                            String value = entry.getValue();
                            
                            // Replace value with environment variables
                            value = substituteEnvironmentVar(value);
                            switch (key)
                            {
                                case "SYSTEMTYPE":
                                    currSysProps.put("WORKFLOW_SYS_TYPE", value);
                                    break;
                                    
                                case "MAXCONNECTIONS":
                                    int poolSize = 10; // Default
                                    try
                                    {
                                        poolSize = Integer.parseInt(value);
                                        poolSize /= 2; // Halve it and distribute to sync/async
                                    }
                                    catch (Exception e)
                                    {
                                    }

                                    // Translate the connection pool size to
                                    // max_sync and async
                                    currSysProps.put("MAX_SYNC_CONNECTIONS", Integer.toString(poolSize));
                                    currSysProps.put("MAX_ASYNC_CONNECTIONS", Integer.toString(poolSize));
                                    break;
                                    
                                case "PASSWORD":            // Have to change it to passwd
                                    currSysProps.put("PASSWD", value);
                                    break;
                                    
                                case "LANGUAGE":            // Have to change it to lang
                                    currSysProps.put("LANG", value);
                                    break;
                                    
                                case "BAPIEXPLORER_USER":           // Skip
                                case "BAPIEXPLORER_PASSWORD":       // Skip
                                    break;

                                default:
                                    currSysProps.put(key, value);
                                    break;
                            }
                        }
                    }

                    // Set the system properties for this request submission
                    principal.setCurrentWfSystemProps(currSysProps);

                    logInfo("execute", "Processed request input, initiating call to SAP");
                    
                    // Exec based on type of operation
                    if (operation.equalsIgnoreCase("beginsession")) {
                        logInfo("execute", "Starting SESSION");
                        
                        rfcRequest = (ISAPRFCRequest) process.createRequest(RequestType.RFC);
                        rfcRequest.setOperation(ISAPRFCRequest.Operation.BeginSession);
                        process.getService().submitRequest(rfcRequest, functionName);
                        dataRows = addSuccess("Session start. ConvID: " + process.getConvId());
                    } else  if (operation.equalsIgnoreCase("endsession")) {
                        logInfo("execute", "Ending SESSION");
                        
                        rfcRequest = (ISAPRFCRequest) process.createRequest(RequestType.RFC);
                        rfcRequest.setOperation(ISAPRFCRequest.Operation.EndSession);
                        process.getService().submitRequest(rfcRequest, functionName);
                        dataRows = addSuccess("Session end. ConvID: " + process.getConvId());
                    }else {            
                        // Execute
                        logInfo("execute", "Starting EXECUTE");
                        
                        rfcRequest = (ISAPRFCRequest) process.createRequest(RequestType.RFC);
                        rfcRequest.setFunction(rfcFunc);
                        ISAPRFCResponse resp = (ISAPRFCResponse) process.getService().submitRequest(rfcRequest, functionName);
    
                        // If success return is the httpcode, all headers and the returned data
                        if (resp.isSuccess())
                        {
                            logInfo("execute", "Execute successful, processing SAP output");
                            
                            // First check if BAPIRET2 is present?
                            if (checkForBAPIRET2Errors(rfcFunc)) {
                                dataRows = processResult(rfcFunc, rfcOutputs);
                            } else {
                                for (InfoMessage msg: infoMessages)
                                    error += msg.getMessage() + "\n";
                            }
                        }
                        else
                        {
                            // Get the error message and additionally any info
                            // messages.
                            process.getInfoMessageList().addAll(resp.getInfoMessages());
                            List<InfoMessage> messages = resp.getInfoMessages();
                            for (InfoMessage msg : messages)
                                if (msg.getCategory() == InfoMessageCategory.FAILURE)
                                    error += msg.getMessage() + "\n";
                        }
                    }
                }
                else
                {
                    error = "SAP System properties to connect to not provided";
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
                error = "System Error: " + e.getMessage();
            }
        }

        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
        {
            logInfo("execute", "Processing complete, returning results");
        }
        else
            logError("execute", error);

        return results;
    }
    
    private String addSuccess(String message) throws Exception
    {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();
        result.put("result", message);
        return mapper.writeValueAsString(result);
    }

    private void processInput(ObjectMapper mapper, WFRFCFunction rfcFunc, JsonNode rfcInputs, String data) throws Exception
    {
        LinkedHashMap<String, JsonNode> dataMap = mapper.readValue(data, new TypeReference<LinkedHashMap<String, JsonNode>>() {});
        
        for (JsonNode rfcInput: rfcInputs) {
            if (dataMap.containsKey(rfcInput.asText())) {
                // Is this a table/structure or direct value
                List<ParameterMeta> params = rfcFunc.getFunctionDescriptor().getParameters();
                for (ParameterMeta param: params) {
                    if (param.getName().equalsIgnoreCase(rfcInput.asText())) {
                        if (param.getType().getSapType() == TypeMeta.TYPE_STRUCTURE) {
                            rfcFunc.setValue(param.getIndex(), processInputStructure(mapper, rfcFunc, param.getType().getName(), dataMap.get(rfcInput.asText())));
                        } else if (param.getType().getSapType() == TypeMeta.TYPE_TABLE) {
                            JsonNode nodes = dataMap.get(rfcInput.asText());
                            WFRFCTable wfrfcTable = (WFRFCTable) rfcFunc.getCachedTable(param.getType().getName());
                            if (nodes.isArray()) {      // Process multiple items?
                                for (JsonNode node: nodes) {
                                    wfrfcTable.addRecord(processInputStructure(mapper, rfcFunc, wfrfcTable.getMetaData().getName(), node));
                                }
                            } else {        // Single record to be added to table
                                wfrfcTable.addRecord(processInputStructure(mapper, rfcFunc, wfrfcTable.getMetaData().getName(), nodes));
                            }
                            rfcFunc.setValue(param.getIndex(), wfrfcTable);
                        } else {
                            rfcFunc.setValue(param.getIndex(), dataMap.get(rfcInput.asText()).asText());
                        }
                        break;
                    }
                }
            }
        }
    }
    
    private WFRFCStructure processInputStructure(ObjectMapper mapper, WFRFCFunction rfcFunc, String structName, JsonNode input) throws Exception
    {
        LinkedHashMap<String, Object> dataMap = mapper.readValue(input.toString(), new TypeReference<LinkedHashMap<String, Object>>() {});
        WFRFCStructure struct = (WFRFCStructure) rfcFunc.getCachedStructure(structName);
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            int index = struct.getFieldIndex(entry.getKey());
            if (index != -1 && entry.getValue() != null) {
                FieldMeta fldMeta = struct.getMetaData().getFields().get(index);
                if (fldMeta.getType().getSapType() == TypeMeta.TYPE_DATE) {
                    // We only accept -, replace any / with -
                    String dateStr = entry.getValue().toString().replace("/", "-");
                    struct.setValue(index, Date.valueOf(dateStr));
                } else if (fldMeta.getType().getSapType() == TypeMeta.TYPE_TIME) {
                    struct.setValue(index, Time.valueOf(entry.getValue().toString()));
                } else {
                    struct.setValue(index, entry.getValue());
                }   
            }
        }
        
        return struct;
    }


    private String processResult(WFRFCFunction rfcFunc, JsonNode rfcOutputs) throws Exception
    {
        JsonObject rslt = new JsonObject();
        for (JsonNode rfcOutput: rfcOutputs) {
            Object obj = rfcFunc.getResult(rfcOutput.asText());
            if (obj != null) {      // Is it a table or a structure
                if (obj instanceof WFRFCStructure) {
                    rslt.add(rfcOutput.asText(), processStructure((WFRFCStructure) obj));
                } else if (obj instanceof WFRFCTable) {
                    JsonArray array = new JsonArray();
                    WFRFCTable table = (WFRFCTable) obj;
                    List<Structure> rows = table.getRecordsList();
                    for (Structure row: rows)
                        array.add(processStructure((WFRFCStructure) row));
                    rslt.add(rfcOutput.asText(), array);
                } else {
                    setValueByType(rslt, rfcOutput.asText(), obj);
                }
            }
        }
        
        return new Gson().toJson(rslt);
    }
    
    private JsonObject processStructure(WFRFCStructure struct)
    {
        JsonObject row = new JsonObject();
        Set<Entry<Integer, Object>> entries = struct.getAllValues();
        for (Entry<Integer, Object> entry: entries) {
            setValueByType(row, struct.getFieldName(entry.getKey()), entry.getValue());
        }
        return row;
    }

    private boolean checkForBAPIRET2Errors(WFRFCFunction rfcFunc)
    {
        WFRFCStructure bapiReturn = null;
        Object bapiRet2Obj = rfcFunc.getResult("RETURN");
        if (bapiRet2Obj != null && bapiRet2Obj instanceof WFRFCTable) {
            WFRFCTable bapiRet2 = (WFRFCTable) bapiRet2Obj;
            List<Structure> rows = bapiRet2.getRecordsList();
            for (Structure row: rows) {
                bapiReturn = (WFRFCStructure) row;
                break;
            }
        } else if (bapiRet2Obj != null && bapiRet2Obj instanceof WFRFCStructure) {
            bapiReturn = (WFRFCStructure) bapiRet2Obj;
        }
        
        if (bapiReturn != null) {
            InfoMessage infoMessage = null;
            String type = bapiReturn.getValue("TYPE").toString();
    
            if ("A".equalsIgnoreCase(type) || "X".equalsIgnoreCase(type) || "E".equalsIgnoreCase(type)) {
                infoMessage = new InfoMessage(bapiReturn.getValue("MESSAGE").toString(), InfoMessage.InfoMessageType.APPLICATION, InfoMessage.InfoMessageCategory.FAILURE);
                if (infoMessages == null) infoMessages = new Vector<InfoMessage>();
                infoMessages.add(infoMessage);
                return false;
            }               
        }
        
        return true;
    }

    private void setValueByType(JsonObject jobj, String key, Object value)
    {
        if (Boolean.class.isInstance(value))
            jobj.addProperty(key, (Boolean) value);
        else if (LocalDateTime.class.isInstance(value))
            jobj.addProperty(key, value.toString());
        else if (Number.class.isInstance(value))
            jobj.addProperty(key, (Number) value);
        else 
            jobj.addProperty(key, value != null ? value.toString() : "");
    }
}
