package com.unvired.operation;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.common.WFStoredProc;
import com.unvired.jdbc.meta.ColumnMeta;
import com.unvired.jdbc.proxy.Procedure;
import com.unvired.jdbc.proxy.Row;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.IJDBCRequest;
import com.unvired.ump.agent.IJDBCResponse;

public class DBStoredProcedureRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_DB_PART1 = "ADHOC_";
    private static final String ADHOC_FUNCTION_DB_PART2 = "_DB_JDBC_EXECUTE";
    
    @Action(name = "storedproc",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "system", required = true) String system,
            @Param(value = "spname", required = true) String spname,
            @Param(value = "spinput", required = false) String spinput,
            @Param(value = "errormessageonnodata", required = false) String errorMsgOnNoData) 
    {
        
        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";
        
        try
        {
            init(convId);
            Iterator<List<Row>> iterator = null;
            IJDBCRequest jdbcRequest = (IJDBCRequest) process.createRequest(RequestType.JDBC);
            jdbcRequest.setRequestType(IJDBCRequest.Mode.Procedure);
            
            logInfo("execute", "Preparing call");
            logDebug("execute", "spname: " + spname);
            logDebug("execute", "spinput: " + spinput);
            
            // Extract out the system props for this system
            Map<String, String> allSysProps = principal.getAllWfSystemProps();
            if (allSysProps != null)
            {
                logInfo("execute", "Processing System Properties");
                
                HashMap<String, String> currSysProps = new HashMap<String, String>();
                String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_DB_PART1;
                
                // Add the system name as an extra parameter so that it helps for the caching
                currSysProps.put("WORKFLOW_SYS_NAME", system);
                
                for (Map.Entry<String, String> entry : allSysProps.entrySet())
                {
                    if (entry.getKey().startsWith(system))
                    {
                        String key = entry.getKey().substring(system.length()+1).toUpperCase();
                        if (key.equals("SYSTEMTYPE"))
                        {
                            currSysProps.put("WORKFLOW_SYS_TYPE", entry.getValue());
                        }
                        else if (key.equals("DBTYPE"))
                        {
                            functionName += entry.getValue().toUpperCase() + ADHOC_FUNCTION_DB_PART2;
                            currSysProps.put("DB_TYPE", entry.getValue());
                        }
                        else if (key.equals("CONNECTIONPOOL"))
                        {
                            int poolSize = 10;      // Default
                            try
                            {
                                poolSize = Integer.parseInt(entry.getValue());
                                poolSize /= 2;          // Halve it and distribute to sync/async
                            }
                            catch(Exception e) {}
                            
                            // Translate the connection pool size to max_sync and async
                            currSysProps.put("MAX_SYNC_CONNECTIONS", Integer.toString(poolSize));
                            currSysProps.put("MAX_ASYNC_CONNECTIONS", Integer.toString(poolSize));
                        }
                        else
                            currSysProps.put(key, entry.getValue());
                    }
                }
                
                // If connection pooling is not specified (sync and async are not added) then we need to add the CLIENT_CREDENTIALS value so that connection pooling is disabled
                if (!currSysProps.containsKey("MAX_SYNC_CONNECTIONS"))
                    currSysProps.put("USE_CLIENT_CREDENTIALS", "YES");
                
                // Handle the connection parameters as UMP expects "CONNECTION_PARAMETERS" but we accept "connectionParameters" in the property file to avoid confusion
                String connParam = currSysProps.get("CONNECTIONPARAMETERS");
                if (connParam != null)
                    currSysProps.put("CONNECTION_PARAMETERS", connParam);                
                
                // Parse the input in the order provided
                HashMap<String, Object> inputMap = new LinkedHashMap<String, Object>();
                try
                {
                    if (spinput != null)
                    {
                        logInfo("execute", "Processing Input");
                        
                        ObjectMapper mapper = new ObjectMapper();
                        Map<String, JsonNode> map = mapper.readValue(spinput, new TypeReference<LinkedHashMap<String, JsonNode>>() {});
                        for (Map.Entry<String, JsonNode> entry : map.entrySet())
                        {
                            if (entry.getValue().isContainerNode())
                            {
                                Map<String, Object> paramdetails = mapper.readValue(entry.getValue().toString(), new TypeReference<LinkedHashMap<String, Object>>() {});
                                
                                // The inner map can be either just type or type with value
                                String type = (String) paramdetails.get("type");
                                Object value = paramdetails.get("value");
                                if (type.equalsIgnoreCase("IN") || type.equalsIgnoreCase("INOUT"))
                                {
                                    if (value != null)
                                    {
                                        inputMap.put(entry.getKey(), value);
                                        inputMap.put(entry.getKey() + WFStoredProc.PARAM_TYPE_SUFFIX, type.equalsIgnoreCase("IN") ? WFStoredProc.PARAM_TYPE_IN : WFStoredProc.PARAM_TYPE_INOUT);
                                    }
                                    else
                                    {
                                        error = "Error parsing inputMap - Parameter: " + entry.getKey() + " value not provided";
                                        break;
                                    }
                                }
                                else
                                {
                                    inputMap.put(entry.getKey(), "OUT-PARAM-NO-VAL");           // Put some random value in as we will not use it
                                    inputMap.put(entry.getKey() + WFStoredProc.PARAM_TYPE_SUFFIX, WFStoredProc.PARAM_TYPE_OUT);
                                }
                            }
                            else
                            {
                                inputMap.put(entry.getKey(), mapper.convertValue(entry.getValue(), Object.class));
                                inputMap.put(entry.getKey() + WFStoredProc.PARAM_TYPE_SUFFIX, WFStoredProc.PARAM_TYPE_IN);
                            }
                        }
                    }
                }
                catch(Exception e)
                {
                    error = "Error parsing input: " + e.getMessage();
                    e.printStackTrace();
                }
                
                if (error.isEmpty())
                {
                    logInfo("execute", "Processing system properties complete, preparing request");
                    
                    // Set the system properties for this request submission
                    principal.setCurrentWfSystemProps(currSysProps);
                    
                    // Set the stored procedure details
                    jdbcRequest.setProcedure(new WFStoredProc(system, currSysProps, inputMap, spname));
                    
                    //  Call
                    IJDBCResponse jdbcResponse = (IJDBCResponse) process.getService().submitRequest(jdbcRequest, functionName);
        
                    if (jdbcResponse.isSuccess()) 
                    {
                        // Has any mapping been provided?
                        HashMap<String, String> fieldNamesMap = new HashMap<String, String>();        
                        if (error.isEmpty())
                        {
                            logInfo("execute", "Processing complete, extracting data");
                            
                            // Need to check for both rows returned and the INOUT/OUT parameters if any
                            Procedure proc = jdbcResponse.getProcedureOutput();
                            iterator = proc.getResultSets();
                            JsonArray jRowsArray = new JsonArray();
                            if (iterator != null && iterator.hasNext()) 
                            {
                                List<Row> rows = iterator.next();
                                if (rows != null && !rows.isEmpty())
                                {
                                    HashMap<String, Integer> nameToIndex = new HashMap<String, Integer>();
                                    for (Row row : rows) 
                                    {
                                        // Get the columnName to index mapping and then reuse across rows
                                        if (nameToIndex.isEmpty())
                                        {
                                            // Set it to all the columns that have been retrieved
                                            List<ColumnMeta> columns = row.getMetaData().getColumns();
                                            for (ColumnMeta column: columns)
                                            {
                                                nameToIndex.put(column.getDescription(), column.getIndex());
                                                fieldNamesMap.put(column.getDescription(), column.getDescription());
                                            }
                                        }
                                        
                                        // Get all the required fields and add them
                                        JsonObject oneRow = new JsonObject();
                                        for (Map.Entry<String, String> entry : fieldNamesMap.entrySet())
                                        {
                                            Object value = row.getValue(nameToIndex.get(entry.getKey()));
                                            if (value instanceof Number)
                                                oneRow.addProperty(entry.getValue(), (Number) value);
                                            else if (value instanceof Boolean)
                                                oneRow.addProperty(entry.getValue(), (Boolean) value);
                                            else if (value instanceof Character)
                                                oneRow.addProperty(entry.getValue(), (Character) value);
                                            else
                                                oneRow.addProperty(entry.getValue(), "" + value);
                                        }
                                        jRowsArray.add(oneRow);
                                    }
                                    dataRows = new Gson().toJson(jRowsArray);
                                } else {                    // Should we treat no data found as an error?
                                    if (errorMsgOnNoData != null && !errorMsgOnNoData.isEmpty())
                                        error = errorMsgOnNoData;
                                }
                            } else {                    // Should we treat no data found as an error?
                                if (errorMsgOnNoData != null && !errorMsgOnNoData.isEmpty())
                                    error = errorMsgOnNoData;
                            }
                            
                            // Now check for any OUT parameters
                            JsonObject outValues = new JsonObject();
                            for (Map.Entry<String, Object> entry : inputMap.entrySet())
                            {
                                String key = entry.getKey();
                                if (!key.endsWith(WFStoredProc.PARAM_TYPE_SUFFIX))           // Skip param-type key
                                {
                                    int paramtype = (int) inputMap.get(key + WFStoredProc.PARAM_TYPE_SUFFIX);   // Get the param type
                                    if (paramtype == WFStoredProc.PARAM_TYPE_OUT || paramtype == WFStoredProc.PARAM_TYPE_INOUT)
                                    {
                                        Object value = proc.getValue("@" + entry.getKey());
                                        if (value instanceof Number)
                                            outValues.addProperty(entry.getKey(), (Number) value);
                                        else if (value instanceof Boolean)
                                            outValues.addProperty(entry.getKey(), (Boolean) value);
                                        else if (value instanceof Character)
                                            outValues.addProperty(entry.getKey(), (Character) value);
                                        else
                                            outValues.addProperty(entry.getKey(), "" + value);
                                    }
                                }
                            }
                            
                            // If we do have some output data and error is set
                            if (!outValues.entrySet().isEmpty()) {
                                if (!error.isEmpty()) {
                                    logError("execute", "No rows were returned, but output is available. Overwriting error");
                                    logError("execute", "Original error: " + error);
                                    error = "";
                                }
                            }
                                
                            // The output format for stored procedures is:  "rows" : "row-data", "outparams" : "out-data"}
                            JsonObject finalResult = new JsonObject();
                            finalResult.add("rows", jRowsArray);
                            finalResult.add("outparams", outValues);
                            
                            // Set the final result
                            dataRows = new Gson().toJson(finalResult);
                        }
                    } 
                    else
                    {
                        process.getInfoMessageList().addAll(jdbcResponse.getInfoMessages());
                        List<InfoMessage> messages = jdbcResponse.getInfoMessages();
                        for (InfoMessage msg: messages)
                            if (msg.getCategory() == InfoMessageCategory.FAILURE)
                                error += msg.getMessage(); 
                    }
                }
            }
            else
            {
                error = "Database System Properties file not provided";
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = e.getMessage();
        }

        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("execute", "Processing complete, returning results");
        else
            logError("execute", error);
        
        return  results;
    }    
}
