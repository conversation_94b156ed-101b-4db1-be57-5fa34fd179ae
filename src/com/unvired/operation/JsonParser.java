package com.unvired.operation;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import com.api.jsonata4java.expressions.Expressions;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.common.WorkflowCacheUtils;

public class JsonParser extends AbstractWorkflowRequest
{
    @Action(name = "jsonparser",
            outputs = 
        {
                @Output("result"),
                @Output("error")
        },
        responses = 
    {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
    }
            )

    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "sourcedata", required = true) String data,
            @Param(value = "priorresults", required = false) String priorResults,
            @Param(value = "mapexpression", required = true) String map)
    {

        Map<String, String> results = new HashMap<>();
        String error = "";

        try
        {
            init(convId);
            logInfo("jsonparser", "Parsing input data");
            
            String inputData = data, inputMap = map;
            logDebug("jsonparser", "sourcedata: " + inputData);
            inputData = StringUtils.strip(inputData, "\"");
            
            // If prior results has the placeholder ~Will~be~CHANGEd~ replace it with just double quotes to make teh json
            if (priorResults != null)
                priorResults = priorResults.replace("~Not~FOUND~", "\"\"");
            else
                priorResults = "{}";
            
            // If there are leading/trailing " remove them for json
            // This is due to the way the expressions are generated as they can have other fields also
            if (inputMap.startsWith("\"") && inputMap.endsWith("\"")) {
                inputMap = inputMap.substring(1, inputMap.length()-1);
                inputMap = inputMap.replace("\\\"", "\"");                  // Generated as \" needs to be "
            }

            logDebug("jsonparser", "mapexpression: " + inputMap);
            logDebug("jsonparser", "priorresults: " + priorResults);

            // Need to process DataMapper inputData also for priorresult placeholders and replace with actual results
            inputData = substitutePrioResults(convId, inputData, priorResults);
            logDebug("jsonparser", "sourcedata final processed: " + inputData);

            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(inputData);
            
            // The priorresult placeholders need to be replaced with actual results
            inputMap = substitutePrioResults(convId, inputMap, priorResults);
            
            // Parse the JSONata expression
            logInfo("jsonparser", "Parsing expression");
            Expressions jsonata = Expressions.parse(inputMap);
            
            logInfo("jsonparser", "Extracting data");
            JsonNode result = jsonata.evaluate(root);
            
            // What kind of result do we have?  JSON or others?
            if (result != null) {
                
                String strResult = "";
                if (result.isContainerNode())
                    strResult = mapper.writeValueAsString(result);
                else
                    strResult = result.asText();
                
                logDebug("jsonparser",  "Extracted data: " + strResult);
                results.put("result", strResult);
                
            } else {
                results.put("result", "");
                logInfo("jsonparser", "Could not extract, returning empty string");
            }

        }
        catch (Exception e)
        {
            e.printStackTrace();
            results.put("result", "");
            error = "Json parser error: " + e.getMessage();
        }

        // Add the error
        results.put("error", error);

        if (error.isEmpty())
            logInfo("jsonparser", "Data extracted successfully, returning results");
        else
            logError("jsonparser", error);
        
        return  results;
    }
    
    // Replace all prior results placeholdesr that are indicated as ___xxx_result___ and changes them to their values
    private String substitutePrioResults(String convId, String expression, String priorResults) throws Exception
    {
        logInfo("jsonparser", "Substituting prior results");
        
        // Retrive variables from cache .. incrementally variable/result will keep getting added to it
        Map<String, Object> varsInCache = WorkflowCacheUtils.getVariables(convId); 
        
        LinkedHashMap<String, JsonNode> priors = new LinkedHashMap<String, JsonNode>();
        
        if (priorResults != null && !priorResults.isEmpty()) {
            ObjectMapper mapper = new ObjectMapper();
            priors = mapper.readValue(priorResults, new TypeReference<LinkedHashMap<String, JsonNode>>() { });
        }

        String regex = "___[a-zA-Z\\\\\\\\-_0-9]*___";
        
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(expression);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) 
        {
            String resultKey = expression.substring(matcher.start()+3, matcher.end()-3);
            String replacement = "";
            if (priors.containsKey(resultKey)) {
                JsonNode pnode = priors.get(resultKey); 
                if (pnode.isTextual())
                    replacement = pnode.asText();
                else
                    replacement = pnode.toString();
            }
            
            // If not found, check in calculated values
            if (replacement.isEmpty()) {
                
                // If its a previous calculation result need to remove the step prefix
                if (resultKey.contains("_") && !resultKey.endsWith("_result")) {
                    resultKey = resultKey.substring(resultKey.indexOf("_")+1);
                }
 
                Object val = varsInCache.get(resultKey);
                if (val != null) {
                    if (val instanceof BigDecimal)
                        replacement = ((BigDecimal) val).toPlainString();
                    else
                        replacement = "\"" + val.toString() + "\"";         // TODO:: need to confirm if this is right
                } else {
                    replacement = "";
                }
            }
            
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            logDebug("jsonparser", "Substituted: " + resultKey + " with: " + replacement);
        }    
        matcher.appendTail(sb);
        return sb.toString();
    }

}
