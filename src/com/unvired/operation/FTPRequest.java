package com.unvired.operation;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.IFTPFile;
import com.unvired.ump.agent.IFTPRequest;
import com.unvired.ump.agent.IFTPRequest.FTPCommand;
import com.unvired.ump.agent.IFTPRequest.FTPMode;
import com.unvired.ump.agent.IFTPRequest.FTPConnectMode;
import com.unvired.ump.agent.IFTPResponse;
import com.unvired.ump.attachment.AttachmentMeta;
import com.unvired.ump.attachment.AttachmentSource;
import com.unvired.ump.attachment.AttachmentState;
import com.unvired.ump.attachment.IAttachment;
import org.apache.commons.io.IOUtils;

public class FTPRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_FTP = "ADHOC_FTP_PORT_FTP_EXECUTE";
    
    @Action(name = "ftp",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "system", required = true) String system,
            @Param(value = "operation", required = true) String operation,
            @Param(value = "mode", required = true) String mode,
            @Param(value = "currentdirectory", required = true) String currentDir,
            @Param(value = "newdirectory", required = false) String newDir,
            @Param(value = "attachmentid", required = false) String attId,
            @Param(value = "mimetype", required = false) String mimeType,
            @Param(value = "filename", required = false) String filename,
            @Param(value = "newfilename", required = false) String newfilename,
            @Param(value = "connectionmode", required = false) String connMode) 
    {
        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";

        try
        {
            init(convId);
            
            logInfo("execute", "Validating input and FTP operations");
            logDebug("execute", "system: " + system);
            logDebug("execute", "operation: " + operation);
            logDebug("execute", "mode: " + mode);
            logDebug("execute", "currentdirectory: " + currentDir);
            logDebug("execute", "filename: " + (filename != null ? filename : ""));
            logDebug("execute", "newfilename: " + (newfilename != null ? newfilename : ""));
            logDebug("execute", "attachmentid: " + (attId != null ? attId : ""));
            logDebug("execute", "mimetype: " + (mimeType != null ? mimeType : ""));
            logDebug("execute", "newdirectory: " + (newDir != null ? newDir : ""));
            logDebug("execute", "connectionmode: " + (connMode != null ? connMode : ""));
        
            IFTPRequest ftpRequest = (IFTPRequest) process.createRequest(RequestType.FTP);
            FTPCommand cmd = null;
            FTPMode transferMode = null;
            FTPConnectMode connectionMode = null;
            
            try
            {
                cmd = Enum.valueOf(FTPCommand.class, operation.toUpperCase());
                ftpRequest.setCommand(cmd);
                
                transferMode = Enum.valueOf(FTPMode.class, mode.toUpperCase());
                ftpRequest.setMode(transferMode);
                
                if (connMode != null && !connMode.isEmpty()) {
                    connectionMode = Enum.valueOf(FTPConnectMode.class, connMode.toUpperCase());
                    ftpRequest.setConnectMode(connectionMode);
                }
                
                // Get the additional parameters
                if (cmd == FTPCommand.PUT) {
                    if (filename == null && attId == null) {
                        error = "Filename and Attachment Id (cache) needs to be specified for PUT operation";
                    } else {
                        ftpRequest.setFileName(filename);
                        
                        // Get the attachment and set the file stream
                        IAttachment att = process.getAttachmentService().getAttachmentByID(attId);
                        if (att == null) {
                            error = "Attachmend Id (cache): " + attId + " not found.";
                        } else if (att.getState() != AttachmentState.CACHED) {
                            error = "Attachmend Id (cache): " + attId + " not yet available, please try later.";
                        } else {
                            // Get the file from attachment cache and copy it to ftprequest
                            InputStream stream = process.getAttachmentService().readAttachment(attId);
                            OutputStream fileOutStream = ftpRequest.getOutputStream();
                            IOUtils.copy(stream, fileOutStream);
                            stream.close();
                            fileOutStream.close();
                        }
                    }
                } else if (cmd == FTPCommand.GET || cmd == FTPCommand.DELETE) {
                    if (filename == null) {
                        error = "Filename needs to be specified for GET/DELETE operations";
                    } else {
                        ftpRequest.setFileName(filename);                        
                    }
                } else if (cmd == FTPCommand.MKDIR) {
                    if (newDir == null) {
                        error = "Directory to create needs to be specified.";
                    } else {
                        ftpRequest.setDirectoryName(newDir);
                    }
                } else if (cmd == FTPCommand.LIST && filename != null) { // Check if filter passed in
                    ftpRequest.setFileName(filename);
                } else if (cmd == FTPCommand.RENAME) {
                    if (filename == null)
                        error = "Remote source filename (full path) needs to be specified for RENAME operation";
                    else
                        ftpRequest.setFileName(filename);
                    if (newfilename == null)
                        error = "Remote destination filename (full path) needs to be specified for RENAME operation";
                    else
                        ftpRequest.setNewFileName(newfilename);                    
                }
            }
            catch (IllegalArgumentException e)
            {
                e.printStackTrace();
                if (cmd == null)
                    error = "Invalid operation: Available actions are Get, Put, Delete, CreateDir and List";
                else if (transferMode == null)
                    error = "Invalid mode. Available modes are ASCII or BINARY";
            } catch (Exception ex) {
                ex.printStackTrace();
                error = "System error: " + ex.getMessage();
            }
            
            // All data input validated and enums initialized?
            if (error.isEmpty())
            {
                logInfo("execute", "Operation identified, setting up request object");
                
                // Set the working directory
                ftpRequest.setWorkingDirectory(currentDir);

                // Extract out the system props for this system
                Map<String, String> allSysProps = principal.getAllWfSystemProps();
                if (allSysProps != null)
                {
                    logInfo("execute", "Handling system properties for system: " + system);
                    
                    HashMap<String, String> currSysProps = new HashMap<String, String>();
                    String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_FTP;
                    
                    // Add the system name as an extra parameter so that it helps for the caching
                    currSysProps.put("WORKFLOW_SYS_NAME", system);
                    
                    for (Map.Entry<String, String> entry : allSysProps.entrySet())
                    {
                        if (entry.getKey().startsWith(system))
                        {
                            String key = entry.getKey().substring(system.length()+1).toUpperCase();
                            if (key.equals("SYSTEMTYPE"))
                            {
                                currSysProps.put("WORKFLOW_SYS_TYPE", entry.getValue());
                            }
                            else
                                currSysProps.put(key, entry.getValue());
                        }
                    }
                    
                    // Set the system properties for this request submission
                    principal.setCurrentWfSystemProps(currSysProps);
                    
                    logInfo("execute", "Executing FTP request");
                    
                    //  Call
                    IFTPResponse ftpResponse = (IFTPResponse) process.getService().submitRequest(ftpRequest, functionName);
        
                    if (ftpResponse.isSuccess()) 
                    {
                        logInfo("execute", "Call done, processing response");
                        
                        if (error.isEmpty())
                        {
                            switch (cmd)
                            {
                                case MKDIR:
                                    dataRows = "{\"data\":\"Folder: " + newDir + " created\"}";
                                    break;
                                    
                                case RENAME:
                                    dataRows = "{\"data\":\"File " + filename + " renamed to: " + newfilename + "\" }";
                                    break;
                                    
                                case PUT:
                                    dataRows = "{\"data\":\"Cached file: " + attId + " uploaded\"";
                                    break;
                                    
                                case DELETE:
                                    dataRows = "{\"data\":\"File: " + filename + " deleted\"}";
                                    break;
                                    
                                case GET:
                                    String uid = UUID.randomUUID().toString();
                                    AttachmentMeta meta = new AttachmentMeta();
                                    meta.setUid(uid);
                                    meta.setCache(true);
                                    meta.setFileName(filename);
                                    meta.setDescription("For ftp workflow");
                                    meta.setMimeType(mimeType);
                                    meta.setSource(AttachmentSource.Workflow);
                                    meta.setExternalReference(uid);
                                    IAttachment newAtt = process.getAttachmentService().addAttachment(meta);
                                    
                                    InputStream stream = ftpResponse.getInputStream();
                                    process.getAttachmentService().loadAttachment(newAtt.getUid(), stream);
                                    stream.close();
                                    dataRows = "{\"data\":\"" + newAtt.getUid() + "\"}";
                                    break;
                                    
                                case LIST:
                                    IFTPFile[] dirs = ftpResponse.getDirectories();
                                    JsonArray dirslist = new JsonArray();
                                    for (IFTPFile dir: dirs) {
                                        JsonObject obj = new JsonObject();
                                        obj.addProperty("name", dir.getName());
                                        obj.addProperty("size", dir.getSize());
                                        obj.addProperty("timestamp", dir.getTimestamp());
                                        dirslist.add(obj);
                                    }
                                    
                                    IFTPFile[] files = ftpResponse.getFiles();
                                    JsonArray fileslist = new JsonArray();
                                    for (IFTPFile file: files) {
                                        JsonObject obj = new JsonObject();
                                        obj.addProperty("name", file.getName());
                                        obj.addProperty("size", file.getSize());
                                        obj.addProperty("timestamp", file.getTimestamp());
                                        fileslist.add(obj);
                                    }
                                    
                                    JsonObject all = new JsonObject();
                                    all.add("directories", dirslist);
                                    all.add("files", fileslist);
                                    JsonObject data = new JsonObject();
                                    data.add("data", all);
                                    dataRows = new Gson().toJson(data);
                                    break;
                            }
                        }
                    } 
                    else
                    {
                        // Get the error message and additionally any info messages.
                        List<InfoMessage> messages = ftpResponse.getInfoMessages();
                        for (InfoMessage msg: messages)
                            if (msg.getCategory() == InfoMessageCategory.FAILURE)
                                error += msg.getMessage() + "\n"; 
                    }
                }
                else
                {
                    error = "FTP System properties to connect to not provided";
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = e.getMessage();
        }

        if (!dataRows.isEmpty())
            logDebug("execute", "Data: " + dataRows);
        
        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("execute", "Processing complete, returning results");
        else
            logError("execute", error);
        
        return  results;
    }    
}
