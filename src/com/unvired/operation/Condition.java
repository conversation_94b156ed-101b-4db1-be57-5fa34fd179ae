package com.unvired.operation;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.MissingNode;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.common.WorkflowCacheUtils;
import com.unvired.evaluator.Evaluator;
import com.unvired.evaluator.EvaluatorBuilder;

public class Condition extends AbstractWorkflowRequest
{
    @Action(name = "condition",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "then", field = "result", value = "true", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "else", field = "result", value = "false", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> evaluate(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "formdata", required = true) String data,
            @Param(value = "priorresults", required = true) String priorResults,            
            @Param(value = "query", required = true) String rules)
    {
        Map<String, String> results = new HashMap<>();
        String error = "", result = "";
        
        try
        {
            init(convId);
            
            logInfo("evaluate", "Preparing call, convID: " + convId);
            
            // Retrive variables from cache .. incrementally variable/result will keep getting added to it
            Map<String, Object> varsInCache = WorkflowCacheUtils.getVariables(convId); 
            Map<String, String> fieldsType = WorkflowCacheUtils.getFields(convId);
            if (fieldsType == null) {
                logError("evaluate", "Could not retrieve fields for conversationID:" + convId);
            }
            
            // If prior results has the placeholder ~Will~be~CHANGEd~ replace it with just double quotes to make teh json
            if (priorResults != null)
                priorResults = priorResults.replace("~Not~FOUND~", "\"\"");
            
            logDebug("evaluate", "priorresults: " + priorResults);
            logDebug("evaluate", "conditions: " + rules);
        
            Evaluator evaluator = new EvaluatorBuilder().buildFromJSON(alias, convId, rules, logger);

            // Fetch the data
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(data);
            
            List<String> usedVars = evaluator.getUsedVariables();
            for (String var: usedVars)
            {
                if (var.startsWith("env_")) {
                    String envVar = var.substring(4);
                    if (envVars != null && envVars.containsKey(envVar)) {
                        logDebug("evaluate", "+++++++++++++++ environment value: " + envVars.get(envVar));
                        evaluator.with(var, evaluator.createSafeString(envVars.get(envVar)));
                    }
                    else {
                        logInfo("evaluate", "+++++++++++++++ environment value not found");
                        evaluator.with(var, "");
                    }
                } else {
                    String type = "string";         // default type is string
                    if (fieldsType.get(var) != null)
                        type = fieldsType.get(var);
                    else
                        logInfo("evaluate", "Field type defaulted to string for: " + var);
    
                    String jsonPath = "/" + var.replace(".", "/");              // search is via / and not .
                    JsonNode token = root.at(jsonPath);
                    if (!(token instanceof MissingNode) && !token.isObject())
                    {
                        logDebug("evaluate", "*** Setting var: " + var + ", value: " + token.asText() + ", type: " + type);
                        if (type.equals("number") || type.equals("integer") || type.equals("double"))
                            evaluator.with(var, new BigDecimal(token.asText()));
                        else
                            evaluator.with(var, evaluator.createSafeString(token.asText()));
                    }
                    else {
                        logError("evaluate", "Value for:" + jsonPath + " not found in formdata. Setting to empty");
                        if (type.equals("number") || type.equals("integer") || type.equals("double"))
                            evaluator.with(var, new BigDecimal("0"));
                        else
                            evaluator.with(var, "");
                    }
                }
            }
            
            // Add the results from the expressions calculated so far using their variable names as they may be used in these exprssions
            for (Entry<String, Object> entry : varsInCache.entrySet()) {
                Object val = entry.getValue();
                if (val != null) {
                    if (val instanceof BigDecimal) {
                        evaluator.with(entry.getKey(), (BigDecimal) val);
                    } else {
                        evaluator.with(entry.getKey(), evaluator.createSafeString(val.toString()));
                    }
                }
            }
            
            // Add the prior results .. input is a JSON of results name and value
            logDebug("evaluate", "extracting prior results");
            if (priorResults != null && !priorResults.isEmpty()) {
                JsonNode rsltArray = mapper.readTree(priorResults);
                Iterator<String> it = rsltArray.fieldNames();
                while (it.hasNext())
                {
                    String key = it.next();
                    String value = rsltArray.findValue(key).toString();

                    // Is it a script or a different node?  If result key exists then its considered to be a different step and should be used as is
                    try {
                        JsonNode valuetree = mapper.readTree(value);
                        // Is this the only key and is it result?
                        if (valuetree.has("result") && valuetree.size() == 1) {
                            evaluator.with(key, evaluator.createSafeString(value));                     // Workflow saves it as step_result
                            logDebug("evaluate", "*************** key: " + key + ", value: " + value);
                        } else {
                            boolean textNode = true;
                            Iterator<String> itval = valuetree.fieldNames();
                            while (itval.hasNext()) {
                                key = itval.next();
                                boolean lazyNum = false;
                                JsonNode vnode = valuetree.findValue(key);
                                if (vnode.isTextual()) {
                                    lazyNum = true;
                                    value = vnode.asText();
                                }
                                else
                                    value = vnode.toString();
                                
                                // Possibly a script node, skip error as a variable
                                if (!key.equalsIgnoreCase("error")) {
                                    if (lazyNum)
                                        evaluator.with(key, evaluator.createSafeString(value));
                                    else
                                        evaluator.with(key, value);
                                    logDebug("evaluate", "*************** key: " + key + ", value: " + value);
                                }
                                textNode = false;           // not a single textnode
                            }
                            
                            // If this was a single text node? (strings like "helloworld" are also treated as valid JSON by Jackson)
                            if (textNode) {
                                evaluator.with(key, evaluator.createSafeString(value));
                                logDebug("evaluate", "*************** key: " + key + ", value: " + value);
                            }
                        }
                    } catch (Exception e) {
                        evaluator.with(key, value);                        // just add the value
                        logDebug("evaluate", "*************** key: " + key + ", value: " + value);
                    }                            
                }
            }

            // Evaluate ..
            result = evaluator.eval().compareTo(BigDecimal.ONE) == 0 ? "true" : "false";
            logDebug("evaluate", "Success call end, result:" + result);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = "Expression error: " + e.getMessage();
        }

        results.put("result", result);
        results.put("error", error);

        if (!error.isEmpty())
            logError("evaluate", error);
        
        return  results;
    }
}
