package com.unvired.operation;

import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.IHTTPRequest;
import com.unvired.ump.agent.IHTTPRequest.HTTPVerb;
import com.unvired.ump.agent.IHTTPResponse;

public class HttpRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_REST = "ADHOC_REST_PORT_REST_EXECUTE";
    
    @Action(name = "http",
        outputs = 
        {
            @Output("result"),
            @Output("httpcode"),
            @Output("headers"),
            @Output("resultbody"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "system", required = true) String system,
            @Param(value = "contenttype", required = true) String contentType,
            @Param(value = "accept", required = true) String accept,
            @Param(value = "httpverb", required = true) String httpVerb,
            @Param(value = "postquerystring", required = false) String postQueryString,
            @Param(value = "instanceurl", required = false) String instanceUrl,
            @Param(value = "urlpath", required = false) String inputPath,
            @Param(value = "attachments", required = false) String attachmentIds,
            @Param(value = "headerparameters", required = false) String headerParameters,
            @Param(value = "postparameters", required = false) String postParameters,
            @Param(value = "body", required = false) String body,
            @Param(value = "failonerror", required = false) String failonerror,
            @Param(value = "httpsuccesscodes", required = false) String httpCodes) 
    {
        
        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";
        String finalHttpCode = "", finalHeaders = "", finalBody = "";
        
        JsonObject errInfo = null;
        List<String> attachments = null;
        Hashtable<String, String> headerParams = new Hashtable<String, String>();       // Will have at least content type / accept
        Hashtable<String, String> postParams = null;
        boolean errorOnFailure = Boolean.parseBoolean(failonerror);
        ArrayList<Integer> httpSuccess = null;
        
        // Parse the inputs that are csv or json format
        try
        {
            init(convId);
            logInfo("execute", "Processing Input");
            logDebug("execute", "contenttype: " + contentType);
            logDebug("execute", "accept: " + accept);
            logDebug("execute", "httpverb: " + httpVerb);
            logDebug("execute", "postquerystring: " + (String) (postQueryString != null ? postQueryString : ""));
            logDebug("execute", "instanceurl: " + (String) (instanceUrl != null ? instanceUrl : ""));
            logDebug("execute", "urlpath: " + (String) (inputPath != null ? inputPath : ""));
            logDebug("execute", "httpsuccesscodes: " + (String) (httpCodes != null ? httpCodes : ""));
        
            String attachArray[] = null;
            if (attachmentIds != null)
            {
                attachArray = attachmentIds.split("\\s*,\\s*");
                Collections.addAll(attachments, attachArray);
            }
        
            // Use list of header params to fill in parameters
            if (headerParameters != null && !headerParameters.trim().isEmpty())
            {
                String headerArray[] = headerParameters.split("~\\|~");
                for (String eachHeader: headerArray)
                {
                    String nameVal[] = eachHeader.split("\\|~\\|");
                    headerParams.put(nameVal[0], nameVal[1]);
                }
            }

            // Even if no headerParameters are passed, headerParams will be a valid Hashtable<>
            headerParams.put("Content-Type", contentType);
            headerParams.put("Accept", accept);
            
            // Use list of post params to fill in parameters
            if (postParameters != null && !postParameters.trim().isEmpty())
            {
                String postArray[] = postParameters.split("~\\|~");
                postParams = new Hashtable<String, String>();
                for (String eachPost: postArray)
                {
                    String nameVal[] = eachPost.split("\\|~\\|");
                    postParams.put(nameVal[0], nameVal[1]);
                }
            }
            
            // Get the comma separated https codes to check for success
            if (httpCodes != null) {
                String codes[] = httpCodes.split("\\s*,\\s*");
                if (codes.length > 0) {
                    httpSuccess = new ArrayList<Integer>();
                    for (String code: codes) {
                        httpSuccess.add(Integer.parseInt(code));
                    }
                }
            }
        }
        catch(Exception e)
        {
            error = "Error parsing input: " + e.getMessage();
            e.printStackTrace();
        }
        
        if (error.isEmpty())
        {
            logInfo("execute", "Processed Input, preparing call");

            try
            {
                IHTTPRequest restRequest = (IHTTPRequest) process.createRequest(RequestType.REST);

                // All data input validated and enums initialized?
                logInfo("execute", "Reading and processing System properties: " + system);
                
                // Extract out the system props for this system
                Map<String, String> allSysProps = principal.getAllWfSystemProps();
                if (allSysProps != null)
                {
                    HashMap<String, String> currSysProps = new HashMap<String, String>();
                    String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_REST;
                    
                    // Add the system name as an extra parameter so that it helps for the caching
                    currSysProps.put("WORKFLOW_SYS_NAME", system);
                    
                    String urlPath = "";
                    for (Map.Entry<String, String> entry : allSysProps.entrySet())
                    {
                        if (entry.getKey().startsWith(system))
                        {
                            String key = entry.getKey().substring(system.length()+1).toUpperCase();
                            String value = entry.getValue();
                            
                            // Replace value with environment variables
                            value = substituteEnvironmentVar(value);
                            switch (key)
                            {
                                case "SYSTEMTYPE":
                                    currSysProps.put("WORKFLOW_SYS_TYPE", value);
                                    break;
                                case "URL":
                                    try
                                    {
                                        URL url = new URL(value);
                                        currSysProps.put("SERVER", url.getHost());
                                        currSysProps.put("PORT", Integer.toString(url.getPort() != -1 ? url.getPort() : url.getDefaultPort()));
                                        currSysProps.put("PROTOCOL", url.getProtocol());
                                        urlPath = url.getPath();
                                        if (urlPath.endsWith("/"))
                                            urlPath = urlPath.substring(0, urlPath.length()-1);
                                    }
                                    catch (Exception e)
                                    {
                                        error = "URL: " + value + "is invalid.  Error: " + e.getMessage();
                                        e.printStackTrace();
                                    }
                                    break;
                                    
                                case "ALLOWINSTANCEURL":
                                    currSysProps.put("ALLOW_INSTANCE_URL_OVERRIDE", value.equalsIgnoreCase("true") ? "YES":"NO");
                                    break;
                                    
                                case "AUTHENTICATIONTYPE":
                                    currSysProps.put("AUTHENTICATION_TYPE", value);
                                    break;

                                case "OAUTHPROVIDER":
                                    currSysProps.put("OAUTH_PROVIDER", value);
                                    break;

                                case "USERPARAMETER":
                                    currSysProps.put("USER_PARAMETER_NAME", value);
                                    break;
                                    
                                case "PASSWORDPARAMETER":
                                    currSysProps.put("PASSWORD_PARAMETER_NAME", value);
                                    break;
                                    
                                case "MAXCONNECTIONS":
                                    int poolSize = 10;      // Default
                                    try
                                    {
                                        poolSize = Integer.parseInt(value);
                                        poolSize /= 2;          // Halve it and distribute to sync/async
                                    }
                                    catch(Exception e) {}
                                    
                                    // Translate the connection pool size to max_sync and async
                                    currSysProps.put("MAX_SYNC_CONNECTIONS", Integer.toString(poolSize));
                                    currSysProps.put("MAX_ASYNC_CONNECTIONS", Integer.toString(poolSize));
                                    break;
                                    
                                case "PROXYSERVER":
                                    currSysProps.put("PROXY_HOST", value);
                                    break;

                                case "PROXYPORT":
                                    currSysProps.put("PROXY_PORT", value);
                                    break;

                                case "PROXYUSER":
                                    currSysProps.put("PROXY_USER", value);
                                    break;

                                case "PROXYPASSWORD":
                                    currSysProps.put("PROXY_PASSWORD", value);
                                    break;

                                default:
                                    currSysProps.put(key, value);
                                    break;
                            }
                        }
                    }
                    
                    // Set the system properties for this request submission
                    principal.setCurrentWfSystemProps(currSysProps);
                    
                    // Set additional request properties starting with Path
                    if (inputPath != null)
                    {
                        if (!inputPath.isEmpty() && !inputPath.startsWith("/"))
                            inputPath = "/" + inputPath.trim();
                        restRequest.setPath(urlPath + inputPath);
                    }
                    else
                        restRequest.setPath(urlPath);
                    
                    // InstanceURL
                    if (instanceUrl != null)
                        restRequest.setInstanceURL(instanceUrl);
                        
                    restRequest.setAttachments(attachments);
                    restRequest.setHeaders(headerParams);
                    restRequest.setParameters(postParams);
                    
                    // Post body or as query string?
                    restRequest.setPostQueryString(Boolean.parseBoolean(postQueryString));
                    
                    // Message body?
                    if (body != null)
                        restRequest.setMessageBody(body);
                    
                    // Http verb.  If not provided defaults to get
                    try
                    {
                        restRequest.setVerb(Enum.valueOf(HTTPVerb.class, httpVerb.toUpperCase()));
                    }
                    catch (Exception e)
                    {
                        restRequest.setVerb(HTTPVerb.GET);
                    }

                    logInfo("execute", "Processed request input, executing call");
                    
                    //  Call
                    IHTTPResponse restResponse = (IHTTPResponse) process.getService().submitRequest(restRequest, functionName);
        
                    // If success return is the httpcode, all headers and the returned data
                    if (restResponse.isSuccess()) 
                    {
                        logInfo("execute", "Execute successful, processing output");
                        Gson gson = new Gson();

                        // The result will be returned as a JSON {"httpCode":xxx, "headers":[], "data":""
                        JsonObject result = new JsonObject();
                        int httpReturnCode = restResponse.getCode();
                        result.addProperty("httpCode", httpReturnCode);
                        finalHttpCode = Integer.toString(httpReturnCode);
                        if (restResponse.getHeaders() != null)
                        {
                            logInfo("execute", "Processed returned headers");

                            JsonObject retHeaders = new JsonObject();
                            for (Map.Entry<String, String> e : restResponse.getHeaders().entrySet())
                                retHeaders.addProperty(e.getKey(), e.getValue());
                            result.add("headers", retHeaders);
                            finalHeaders = gson.toJson(retHeaders);
                        }
                        
                        // Return as JSON instead of string .. ignore exception and return as is in case of error
                        JsonElement elem = null;
                        try {
                            JsonParser jsonParser = new JsonParser();
                            elem = jsonParser.parse(restResponse.getMessage());
                        } catch (Exception e) {}
                        
                        if (elem != null) {
                            result.add("data", elem);
                            finalBody = gson.toJson(elem);
                        }
                        else
                            result.addProperty("data", restResponse.getMessage());

                        // Final result
                        dataRows = gson.toJson(result);
                        
                        // Do we need to check http result and indicate error?
                        if (httpSuccess != null) {
                            if (!httpSuccess.contains(httpReturnCode)) {    // The return code is not a success, swap the output to indicate error
                                
                                // Also check if there are any info messages and add
                                error = getInfoMessages(restResponse);
                                if (error.isEmpty())
                                    error = "System Error";
                                
                                errInfo = result;           // Will add additional attributes
                                dataRows = "";
                                finalHttpCode = "";
                                finalHeaders = "";
                                finalBody = "";
                                errorOnFailure = true;      // Since http codes are provided this request will need to fail and indicate an error
                            }
                        }
                    } 
                    else
                    {
                        // Get the error message and additionally any info messages.
                        error = getInfoMessages(restResponse);
                    }
                }
                else
                {
                    error = "REST System properties to connect to not provided";
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
                error = e.getMessage();
            }
        }
        
        // If we need to ignore errors?
        if (!error.isEmpty() && !errorOnFailure) {
            JsonObject result = new JsonObject();
            result.addProperty("data", "Setting result to success as failonerror is false");
            result.addProperty("httpCode", 200);
            result.addProperty("error", "");
            finalHttpCode = "200";
            finalHeaders = "";
            finalBody = "";

            if (errInfo != null)
                result.add("errorInfo", errInfo);
            
            results.put("result", new Gson().toJson(result));
            results.put("error", "");

            logInfo("execute", "Setting result to success as failonerror is false");
        } else {
            results.put("result", dataRows);
            results.put("httpcode", finalHttpCode);
            results.put("headers", finalHeaders);
            results.put("resultbody", finalBody);
            
            if (!error.isEmpty()) {
                JsonObject result = new JsonObject();
                result.addProperty("error", error);
                if (errInfo != null)
                    result.add("errorInfo", errInfo);
                error = new Gson().toJson(result);
                results.put("error", error);
            } else
                results.put("error", "");
        }
        
        if (error.isEmpty())
            logInfo("execute", "Processing complete, returning results");
        else
            logError("execute", error);
        
        return  results;
    }
    
    private String getInfoMessages(IHTTPResponse restResponse)
    {
        String err = "";
        process.getInfoMessageList().addAll(restResponse.getInfoMessages());
        List<InfoMessage> messages = restResponse.getInfoMessages();
        for (InfoMessage msg: messages) {
            if (msg.getCategory() == InfoMessageCategory.FAILURE)
                err += msg.getMessage() + "\n";
        }
        return err;
    }
}
