package com.unvired.operation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.ump.agent.IAgentRequest;
import com.unvired.ump.agent.IAgentResponse;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.context.IUser;

public class SendEmail extends AbstractWorkflowRequest
{
    @Action(name = "email",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "users", required = false) String users,
            @Param(value = "teams", required = false) String teams,
            @Param(value = "emails", required = false) String emails,
            @Param(value = "subject", required = true) String emailSubject,
            @Param(value = "message", required = true) String emailMessage,
            @Param(value = "attachmentids", required = false) String attachmentIds,
            @Param(value = "formdata", required = true) String formData,
            @Param(value = "template", required = true) String template,
            @Param(value = "failonerror", required = false) String failonerror)
    {
        
        Map<String, String> results = new HashMap<>();
        String error = "", result = "";
        
        try {
            init(convId);
        } catch (Exception e) {
            e.printStackTrace();
            error = e.getMessage();
            results.put("result", result);
            results.put("error", error);
        }
        
        if (!error.isEmpty())
            return results;
        
        logInfo("execute", "Preparing call");
        logDebug("execute", "users: " + (users != null ? users : ""));
        logDebug("execute", "teams: " + (teams != null ? teams : ""));
        logDebug("execute", "emails: " + (emails != null ? emails : ""));
        logDebug("execute", "subject: " + emailSubject);
        logDebug("execute", "template: " + template);
        if (attachmentIds != null)
            logDebug("execute", "Attachments: " + attachmentIds);
        
        boolean errorOnFailure = Boolean.parseBoolean(failonerror);
        
        if ((users != null && !users.isEmpty()) || (teams != null && !teams.isEmpty()) || (emails != null && !emails.isEmpty()))
        {
            try
            {
                logInfo("execute", "Preparing call");
                
                List<String> to = new ArrayList<String>();
                if (users != null && !users.isEmpty()) {
                    logDebug("execute", "Processing users");
                    String userEmails[] = users.split("\\s*,\\s*");
                    List<IUser> iUsers = process.getApplicationContext().getUsers();
                    for (String userEmail: userEmails) {
                        for (IUser iUser: iUsers) {
                            if (iUser.getUserID().equalsIgnoreCase(userEmail)) {
                                to.add(iUser.getEMail());
                                break;
                            }
                        }
                    }
                }
                
                // All teams
                if (teams != null && !teams.isEmpty()) {
                    String teamnames[] = teams.split("\\s*,\\s*");
                    for (String team: teamnames) {
                        addTeamEmails(to, team);
                    }
                }
                
                // Add all direct email ids
                if (emails != null && !emails.isEmpty()) {
                    logDebug("execute", "Processing emailids");
                    String extEmails[] = emails.split("\\s*,\\s*");
                    Collections.addAll(to, extEmails);
                }
                
                String[] toMails = new String[to.size()];
                to.toArray(toMails);
                
                String attachments[] = null;
                if (attachmentIds != null) {
                    logDebug("execute", "Processing attachmentids");
                    attachments = attachmentIds.split("\\s*,\\s*");
                }
                
                // Replace any form field values in the email subject and message
                ObjectMapper mapper = new ObjectMapper();
                JsonNode root = mapper.readTree(formData);
                String subject = replaceFormFields(emailSubject, root);
                String message = replaceFormFields(emailMessage, root);

                logInfo("execute", "Executing call");

                process.getService().sendMail(template, null, null, toMails, null, null, subject, message, null, attachments, formData);
                JsonObject jsonResult = new JsonObject();
                jsonResult.addProperty("mail", "" + to.size() + " emails sent");
                result = new Gson().toJson(jsonResult);
                error = "";
            }
            catch (Exception e)
            {
                e.printStackTrace();
                error = "System error: " + e.getMessage();
            }            
        }
        else
        {
            logError("execute", "None of the recipients specified");
            error = "Atleast one of formusers, formteams or emailid should be specified";
        }

        results.put("result", result);
        results.put("error", error);
        
        // If we need to ignore errors?
        if (!errorOnFailure && !error.isEmpty()) {
            results.put("result", "Warning: Emails not sent");
            results.put("error", "");
            logError("execute", "Setting result to success as failonerror is false");
        }

        logInfo("execute", "Processing complete, email queued successfully");
        
        return  results;
    }
    
    // Replaces provided emssage template with ${fieldname} with actual values
    private String replaceFormFields(String template, JsonNode root)
    {
        logInfo("replaceFormFields", "Expanding formfields");
        
        String regex = "\\$\\{[a-zA-Z\\-_0-9]*\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(template);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) 
        {
            String fieldName = template.substring(matcher.start()+2, matcher.end()-1);
            logDebug("replaceFormFields", "Found field: " + fieldName);
            JsonNode node = root.at("/" + fieldName);
            if (!node.isMissingNode())
            {
                matcher.appendReplacement(sb, Matcher.quoteReplacement(node.toString()));
                logDebug("replaceFormFields", "Replaced field: " + fieldName + " with: " + node.toString());
            }
            else
            {
                matcher.appendReplacement(sb, "undefined");
                logError("replaceFormFields", "Field: " + fieldName + " not found in form, cannot replace");
            }
        };            
        matcher.appendTail(sb);
        return sb.toString();
    }
    
    private void addTeamEmails(List<String> to, String team) throws Exception
    {
        logInfo("execute", "Processing teams");
        
        IAgentRequest agentRequest = (IAgentRequest) process.createRequest(RequestType.AGENT);

        // Get the field information based on the submissionId
        JsonObject input = new JsonObject();
        input.addProperty("limit", 1000);           // Should be enough I guess!
        input.addProperty("offset", 0);
        input.addProperty("sort_order", "asc");
        input.addProperty("exclude", "");
        
        JsonObject filter = new JsonObject();
        input.addProperty("teamName", team);
        input.addProperty("excludeUsers", false);
        input.add("filter", filter);

        agentRequest.setInput(new Gson().toJson(input));
        IAgentResponse agentResponse = (IAgentResponse) process.getService().submitRequest(agentRequest, "DIGITAL_FORMS_PA_GET_USERS");
        String rawResponse = agentResponse.getCustomOutput();

        if (rawResponse != null)
        {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(rawResponse).findPath("formUsers");
            if (!root.isMissingNode())
            {
                ArrayList<JsonNode> list = mapper.readValue(root.toString(), new TypeReference<ArrayList<JsonNode>>() {});
                for (JsonNode node : list)
                {
                    String email = node.get("email").asText();
                    to.add(email);
                }
            }
        }
        else
        {
            throw new Exception("Fatal Error: Could not retrieve emails of team members");
        }
    }
}
