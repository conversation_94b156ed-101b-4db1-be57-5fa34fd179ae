package com.unvired.operation;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import com.fasterxml.jackson.core.JsonGenerator.Feature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.ump.attachment.AttachmentState;
import com.unvired.ump.attachment.IAttachment;

public class CSVParser extends AbstractWorkflowRequest
{
    private class FileCacherException extends Exception
    {
        private static final long serialVersionUID = 1L;

        FileCacherException(String error)
        {
            super(error);
        }
    }

    @Action(name = "csvparser",
            outputs = 
        {
                @Output("result"),
                @Output("error")
        },
        responses = 
    {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
    }
            )

    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "inputformat", required = true) String inputFormat,
            @Param(value = "attachmentid", required = false) String attId,
            @Param(value = "data", required = false) String data)
    {

        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";

        try
        {
            init(convId);
            logInfo("csvparser", "Parsing input data");
            logDebug("csvparser", "inputformat: " + inputFormat);

            String inputData = data;
            logDebug("csvparser", "sourcedata: " + (String) (inputData != null ? inputData : ""));
            logDebug("csvparser", "attachmentid: " + (String) (attId != null ? attId : ""));
            
            // If we are converting from CSV the input must be either file or data
            // For JSON its only data
            if (inputFormat.equalsIgnoreCase("CSV")) {
                if (inputData == null && attId == null) {
                    error = "To transform CSV, either data or attachmentid needs to be specified";
                } else {
                    // If attachment download the file and set to inputData
                    if (attId != null)
                        inputData = getFileContents(attId);
                }
                
                // Convert to JSON
                if (error.isEmpty()) {
                    CsvSchema csv = CsvSchema.emptySchema().withHeader();
                    CsvMapper csvMapper = new CsvMapper();
                    List<Map<?, ?>> list = null;
                    try (MappingIterator<Map<?, ?>> mappingIterator = csvMapper.reader().forType(Map.class).with(csv).readValues(inputData)) {
                        list = mappingIterator.readAll();
                    }
                   ObjectMapper mapper = new ObjectMapper();
                   ByteArrayOutputStream stream = new ByteArrayOutputStream(); 
                   mapper.writeValue(stream, list);
                   dataRows = stream.toString("utf-8");
                }
            } else {
                if (inputData == null && attId == null) { 
                    error = "To transform JSON, either data or attachmentid needs to be specified";
                } else {
                    // If attachment download the file and set to inputData
                    if (attId != null)
                        inputData = getFileContents(attId);

                    JsonNode jsonNode = new ObjectMapper().readTree(inputData);
                    
                    // Add all json fields, nested fields are not supported and should be removed before processing by caller
                    CsvSchema.Builder builder = CsvSchema.builder();
                    jsonNode.elements().next().fieldNames().forEachRemaining(f -> builder.addColumn(f));
                    CsvSchema csvSchema = builder.build().withHeader();
                    CsvMapper csvMapper = new CsvMapper();
                    csvMapper.configure(Feature.IGNORE_UNKNOWN, true);
                    dataRows = csvMapper.writerFor(JsonNode.class)
                        .with(csvSchema)
                        .writeValueAsString(jsonNode);
                }
            }
        }
        catch (FileCacherException ex) {
            error = ex.getMessage();
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = "CSV parser error: " + e.getMessage();
        }

        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("csvparser", "Data transformed successfully, returning results");
        else
            logError("csvparser", error);
        
        return  results;
    }
    
    private String getFileContents(String attId) throws Exception
    {
        String inputData = null;
        
        // Get the attachment and set the file stream
        IAttachment att = process.getAttachmentService().getAttachmentByID(attId);
        if (att == null) {
            throw new FileCacherException("Attachmend Id (cache): " + attId + " not found.");
        } else if (att.getState() != AttachmentState.CACHED) {
            throw new FileCacherException("Attachmend Id (cache): " + attId + " not yet available, please try later.");
        } else {
            // Get the file from attachment cache adn copy it to ftprequest
            InputStream stream = process.getAttachmentService().readAttachment(attId);
            inputData = IOUtils.toString(stream, "utf-8");
            stream.close();
        }
        return inputData;
    }
}
