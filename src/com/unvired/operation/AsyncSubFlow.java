package com.unvired.operation;

import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.common.WorkflowCacheUtils;

public class AsyncSubFlow extends AbstractWorkflowRequest
{
    @Action(name = "asyncsubflow",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "formId", required = true) String formId,
            @Param(value = "taskId", required = false) String taskId,
            @Param(value = "submissionId", required = false) String submissionId,
            @Param(value = "flowinput", required = false) String flowInput,
            @Param(value = "formdata", required = false) String formData,
            @Param(value = "formusers", required = false) String formUsers,
            @Param(value = "flowname", required = true) String workflow,
            @Param(value = "isparallel", required = true) String isParallel)
    {
        Map<String, String> results = new HashMap<>();
        String error = "", result = workflow + " queued successfully with Conversation Id: ";

        try
        {
            init(convId);

            logInfo("execute", "Preparing call");
            logDebug("execute", "flowname: " + workflow);
            if (flowInput != null) {
                flowInput = StringUtils.strip(flowInput, "\"");
                logDebug("execute", "flowinput: " + flowInput);
            }
            
            String wfNamespace = namespace.toLowerCase() + ".flow";
            boolean parallel = Boolean.parseBoolean(isParallel);
            String conId = WorkflowCacheUtils.executeWorkflowAsync(parallel, convId, workflow, wfNamespace, flowInput, formId, submissionId, taskId, formUsers, formData);
            result += conId;
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = "System error: " + e.getMessage();
        }

        results.put("result", result);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("execute", result);
        else
            logError("execute", error);
        
        return  results;
    }
}
