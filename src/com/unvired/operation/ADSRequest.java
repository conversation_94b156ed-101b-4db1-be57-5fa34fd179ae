package com.unvired.operation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.lib.utility.InfoMessage.InfoMessageCategory;
import com.unvired.ump.agent.IActiveDirectoryRequest;
import com.unvired.ump.agent.IActiveDirectoryRequest.Modification;
import com.unvired.ump.agent.IActiveDirectoryRequest.Operation;
import com.unvired.ump.agent.IActiveDirectoryRequest.Scope;
import com.unvired.ump.agent.IActiveDirectoryResponse;
import com.unvired.ump.agent.IBusinessProcess.RequestType;

public class ADSRequest extends AbstractWorkflowRequest
{
    private static final String ADHOC_FUNCTION_ADS = "ADHOC_ADS_PORT_ADS_EXECUTE";
    
    @Action(name = "ads",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> query(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "system", required = true) String system,
            @Param(value = "action", required = true) String action,
            @Param(value = "operation", required = false) String attribOpn,
            @Param(value = "searchcontrols", required = false) String searchCtrl,
            @Param(value = "field", required = false) String fieldName,
            @Param(value = "value", required = false) String value,
            @Param(value = "basedn", required = false) String baseDn,
            @Param(value = "domain", required = false) String domain,
            @Param(value = "user", required = false) String user,
            @Param(value = "password", required = false) String password) 
    {
        Map<String, String> results = new HashMap<>();
        String error = "", dataRows = "";

        try
        {
            init(convId);
            
            logInfo("query", "Validating input and ADS operations");
            logDebug("query", "action: " + action);
            logDebug("query", "operation: " + attribOpn != null ? attribOpn : "");
            logDebug("query", "searchCtrl: " + searchCtrl != null ? searchCtrl : "");
            logDebug("query", "field: " + fieldName != null ? fieldName : "");
            logDebug("query", "value: " + value != null ? value : "");
            logDebug("query", "basedn: " + baseDn != null ? baseDn : "");
            logDebug("query", "domain: " + domain != null ? domain : "");
            logDebug("query", "user: " + user != null ? user : "");
        
            IActiveDirectoryRequest adsRequest = (IActiveDirectoryRequest) process.createRequest(RequestType.ActiveDirectory);
            Operation opn = null;
            Modification mod = null;
            Scope scope = null;
            
            try
            {
                opn = Enum.valueOf(Operation.class, action);
                adsRequest.setOperation(opn);
                
                // Get the additional parameters
                if (opn == Operation.SetAttribute)
                {
                    if (fieldName == null || value == null || attribOpn == null)
                    {
                        error = "attributeAction, name and value are mandatory parameters for SetAttribute and need to be provided.";
                    }
                    else
                    {
                        mod = Enum.valueOf(Modification.class, attribOpn);
                        adsRequest.modifyAttribute(fieldName, value, mod);
                    }
                }
                else if (opn == Operation.SearchObjects)
                {
                    if (fieldName == null || value == null || searchCtrl == null)
                    {
                        error = "searchControls, name and value are mandatory parameters for SearchObjects and need to be provided.";
                    }
                    else
                    {
                        scope = Enum.valueOf(Scope.class, searchCtrl);
                        adsRequest.setSearchFilter(fieldName, value, scope);
                    }
                }
            }
            catch (IllegalArgumentException e)
            {
                e.printStackTrace();
                if (opn == null)
                    error = "Invalid action: Available actions are Authenticate, GetAttributes, ListObjects, SearchObjects and SetAttribute";
                else if (opn == Operation.SetAttribute && mod == null)
                    error = "Invalid setAttribute: Available attribute operations are Add, Replace and Delete";
                else
                    error = "Invalid searchControls: Available search controls are Object, OneLevel and SubTree";
            }
            
            // All data input validated and enums initialized?
            if (error.isEmpty())
            {
                logInfo("query", "Operation identified, setting up request object");

                // Extract out the system props for this system
                Map<String, String> allSysProps = principal.getAllWfSystemProps();
                if (allSysProps != null)
                {
                    logInfo("query", "Handling system properties for system: " + system);
                    
                    HashMap<String, String> currSysProps = new HashMap<String, String>();
                    String functionName = application.toUpperCase() + "_" + ADHOC_FUNCTION_ADS;
                    
                    // Add the system name as an extra parameter so that it helps for the caching
                    currSysProps.put("WORKFLOW_SYS_NAME", system);
                    
                    for (Map.Entry<String, String> entry : allSysProps.entrySet())
                    {
                        if (entry.getKey().startsWith(system))
                        {
                            String key = entry.getKey().substring(system.length()+1).toUpperCase();
                            if (key.equals("SYSTEMTYPE"))
                            {
                                currSysProps.put("WORKFLOW_SYS_TYPE", entry.getValue());
                            }
                            else if (key.equals("SSL"))
                            {
                                currSysProps.put("SSL", value.equalsIgnoreCase("true") ? "ENABLED":"DISABLED");
                            }
                            else
                                currSysProps.put(key, entry.getValue());
                        }
                    }
                    
                    // Handle the basedn: UMP expects "BASE_DN" but we accept "basedn" in the property file to avoid confusion between post param and system properties
                    String base_dn = currSysProps.get("BASEDN");
                    if (base_dn != null)
                        currSysProps.put("BASE_DN", base_dn);

                    String domainName = currSysProps.get("DOMAIN");
                    if (domainName != null)
                        adsRequest.setDomain(domainName);
                    
                    // Handle the authenticationtype: UMP expects "AUTHENTICATION_TYPE" but we accept "authenticationtype" in the property file so its all
                    // lower case like other params.  If not found default to SIMPLE
                    String auth_type = currSysProps.get("AUTHENTICATIONTYPE");
                    if (auth_type == null) auth_type = "SIMPLE";
                    currSysProps.put("AUTHENTICATION_TYPE", auth_type);
                    
                    // Set the overriding values in the ADSRequest object which take a higher priority than system properties
                    if (baseDn != null)
                        adsRequest.setDistinguishedName(baseDn);
                    
                    if (domain != null)
                        adsRequest.setDomain(domain);
                    
                    if (user != null)
                        adsRequest.setUser(user);
                    
                    if (password != null)
                        adsRequest.setPassword(password);
                    
                    // Set the system properties for this request submission
                    principal.setCurrentWfSystemProps(currSysProps);
                    
                    logInfo("query", "Executing ADS request");
                    
                    //  Call
                    IActiveDirectoryResponse adsResponse = (IActiveDirectoryResponse) process.getService().submitRequest(adsRequest, functionName);
        
                    if (adsResponse.isSuccess()) 
                    {
                        logInfo("query", "Call done, processing response");
                        
                        if (error.isEmpty())
                        {
                            switch (opn)
                            {
                                case Authenticate:
                                case SetAttribute:
                                    // Return the message from the operation
                                    if (adsResponse.getResponseMessage() != null)
                                        dataRows = adsResponse.getResponseMessage();
                                    break;
                                    
                                case GetAttributes:
                                    Map<String, Object> attributes = adsResponse.getAttributes();
                                    JsonObject attribs = new JsonObject();
                                    for (Map.Entry<String, Object> entry : attributes.entrySet())
                                    {
                                        String key = entry.getKey();
                                        Object val = entry.getValue();
                                        if (val instanceof Number)
                                            attribs.addProperty(key, (Number) val);
                                        else if (val instanceof Boolean)
                                            attribs.addProperty(key, (Boolean) val);
                                        else if (val instanceof Character)
                                            attribs.addProperty(key, (Character) val);
                                        else
                                            attribs.addProperty(key, "" + val);
                                    }
                                    dataRows = new Gson().toJson(attribs);
                                    break;
                                    
                                case ListObjects:
                                case SearchObjects:
                                    JsonArray array = (JsonArray) new Gson().toJsonTree(adsResponse.getObjects(), new TypeToken<List<String>>(){}.getType());
                                    JsonObject object = new JsonObject();
                                    object.add("objects", array);
                                    dataRows = new Gson().toJson(object);
                                    break;
                            }
                        }
                    } 
                    else
                    {
                        // Get the error message and additionally any info messages.
                        if (adsResponse.getResponseMessage() != null)
                            error =  adsResponse.getResponseMessage() + "\n";
                        
                        process.getInfoMessageList().addAll(adsResponse.getInfoMessages());
                        List<InfoMessage> messages = adsResponse.getInfoMessages();
                        for (InfoMessage msg: messages)
                            if (msg.getCategory() == InfoMessageCategory.FAILURE)
                                error += msg.getMessage() + "\n"; 
                    }
                }
                else
                {
                    error = "Active Directory System properties to connect to not provided";
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = e.getMessage();
        }

        if (!dataRows.isEmpty())
            logDebug("query", "Data: " + dataRows);
        
        results.put("result", dataRows);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("query", "Processing complete, returning results");
        else
            logError("query", error);
        
        return  results;
    }    
}
