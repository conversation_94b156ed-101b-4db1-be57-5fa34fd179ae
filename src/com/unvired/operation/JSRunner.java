package com.unvired.operation;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import org.graalvm.polyglot.Context;
import org.graalvm.polyglot.HostAccess;
import org.graalvm.polyglot.PolyglotException;
import org.graalvm.polyglot.Source;
import org.graalvm.polyglot.SourceSection;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.ump.agent.ILogger;

public class JSRunner extends AbstractWorkflowRequest
{
    public class Logger {
        
        private ILogger logger;
        private String  scriptName;
        
        Logger(ILogger logger, String scriptName) {
            this.logger = logger;
            String nameparts[] = scriptName.split("_");
            this.scriptName = "Workflow: " + nameparts[0] + ", Script: " + nameparts[1];
        }
        
        public void error(String message)
        {
            logger.addErrorLog("JSRunner", scriptName, message);
        }
        
        public void info(String message)
        {
            logger.addInfoLog("JSRunner", scriptName, message);
        }
        
        public void debug(String message)
        {
            logger.addDebugLog("JSRunner", scriptName, message);
        }
    }
    
    @Action(name = "execjavascript",
            outputs = 
        {
                @Output("result"),
                @Output("error")
        },
        responses = 
    {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
    }
            )

    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "contentpath", required = true) String contentPath,
            @Param(value = "scriptname", required = true) String scriptName,
            @Param(value = "inputdata", required = true) String inputData,
            @Param(value = "loglevel", required = false) String logLevel)
    {
        
        Map<String, String> results = new HashMap<>();
        String error = "", result = "";
        
        try
        {
            init(convId);
            logInfo("execute", "Preparing to execute");
            logDebug("execute", "contentpath: " + contentPath);
            logDebug("execute", "scriptname: " + scriptName);
            logDebug("execute", "inputdata: " + inputData);

            Map<String, String> options = new HashMap<>();

            // Enable CommonJS experimental support.
            options.put("js.commonjs-require", "true");
            
            // (optional) folder where the NPM modules to be loaded are located.
            options.put("js.commonjs-require-cwd", contentPath + File.separator + "npm-modules");
            
            // (optional) Node.js built-in replacements as a comma separated list.
//            options.put("js.commonjs-core-modules-replacements", "buffer:buffer/," + "path:path-browserify");
            
            try {
                logInfo("execute", "Preparing call, fetching context");
                Context context = Context.newBuilder("js").allowHostAccess(HostAccess.ALL).build();
                context.getBindings("js").putMember("logger", new Logger(process.getLogger(), scriptName));
                
                // Content path ends at adhocscripts with a trailing /
                Source source = Source.newBuilder("js", new File(contentPath + alias.toLowerCase() + File.separator + scriptName + ".js")).build();
                org.graalvm.polyglot.Value parsedSrc = context.parse(source);
                
                // Just to check for any errors
                logDebug("execute", "Checking syntax errors");
                parsedSrc.execute();

                // Get the execute method
                logDebug("execute", "Getting execute() function accessor");
                org.graalvm.polyglot.Value execFn = context.getBindings("js").getMember("execute");
                
                // Evaluate the parsed script
                logInfo("execute", "Executing call");
                org.graalvm.polyglot.Value scriptResult = execFn.execute(inputData, logLevel);
                
                // Access the function result
                logDebug("execute", "Getting function results");
                result = scriptResult.asString();
            } catch (PolyglotException e) {
                logError("execute", "Exception: " + e.getMessage());
                
                if (e.isSyntaxError()) {
                    SourceSection location = e.getSourceLocation();
                    error = "JavaScript syntax error in: " + scriptName;
                    if (location != null)
                        error += " Check: " + location;
                    throw new Exception(error);
                }
                throw e;
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = "System error: " + e.getMessage();
            result = "{\"error\":\"" + error + "\"}";       // Script is a little strange!  Error also extracted from result
        }            
    
        if (!error.isEmpty())
            logError("execute", error);
        
        results.put("result", result);
        results.put("error", error);
    
        logInfo("execute", "Execution of JS: " + scriptName + " complete");
        logDebug("execute", "Results: " + result);
    
        return  results;
    }
}
