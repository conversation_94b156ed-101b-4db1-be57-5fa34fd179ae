package com.unvired.operation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hp.oo.sdk.content.annotations.Action;
import com.hp.oo.sdk.content.annotations.Output;
import com.hp.oo.sdk.content.annotations.Param;
import com.hp.oo.sdk.content.annotations.Response;
import com.hp.oo.sdk.content.plugin.ActionMetadata.MatchType;
import com.unvired.api.AbstractWorkflowRequest;
import com.unvired.lib.utility.InfoMessage;
import com.unvired.ump.agent.IAgentRequest;
import com.unvired.ump.agent.IAgentResponse;
import com.unvired.ump.agent.IBusinessProcess.RequestType;
import com.unvired.ump.agent.impl.PAWorkflowResponse;

public class PARequest extends AbstractWorkflowRequest
{
    @Action(name = "execjava",
        outputs = 
        {
            @Output("result"),
            @Output("error")
        },
        responses = 
        {
            @Response(text = "success", field = "error", value = "", matchType = MatchType.COMPARE_EQUAL),
            @Response(text = "failure", field = "error", value = "", matchType = MatchType.COMPARE_NOT_EQUAL, isDefault = true, isOnFail = true)
        }
    )
    
    public Map<String, String> execute(@Param(value = "alias", required = true) String alias,
            @Param(value = "namespace", required = true) String namespace,
            @Param(value = "application", required = true) String application,
            @Param(value = "convId", required = true) String convId,
            @Param(value = "function", required = true) String function,            
            @Param(value = "fninput", required = false) String fninput)
    {
        Map<String, String> results = new HashMap<>();
        String error = "", result = "";
        String paFunction = function;

        try
        {
            ObjectMapper mapper = new ObjectMapper();
            
            init(convId);

            // If there are leading/trailing " remove them for function name
            // This is due to the way the expressions are generated as they can have other fields also and ObjectMapper reads a simple string also as a valid json
            paFunction = StringUtils.strip(paFunction, "\"");
            
            logInfo("execute", "Preparing call");
            logDebug("execute", "function: " + paFunction);
        
            IAgentRequest agentRequest = (IAgentRequest) process.createRequest(RequestType.AGENT);
            
            // If fnInput is specified..
            if (fninput != null) {
                logDebug("execute", "fninput: " + fninput);
                agentRequest.setInput(fninput);
            } else {
                logInfo("execute", "No input specified, setting to parameters passed in to execute workflow");
                agentRequest.setInput(ibMessage.getWfPostParams()); // Since no input passed in  specifically, use the post parameters passed into the workflow.
            }
            
            logInfo("execute", "Executing call");
            
            //  Call
            IAgentResponse agentResponse = (IAgentResponse) process.getService().submitRequest(agentRequest, paFunction);
            String rawResponse = agentResponse.getCustomOutput();
            logDebug("execute", "Result:" + rawResponse);
            
            try
            {
                // Lets check InfoMessages first
                List<InfoMessage> msgs = agentResponse.getInfoMessages();
                if (msgs.size() > 0) {
                    for (InfoMessage msg: msgs) {
                        if (msg.getCategory().equals(InfoMessage.InfoMessageCategory.FAILURE)) {
                            if (!error.isEmpty()) error += "\n";
                            error = msg.getMessage();
                        }
                    }
                }
                
                if (error.isEmpty() && rawResponse != null)
                {
                    PAWorkflowResponse response = mapper.readValue(rawResponse, new TypeReference<PAWorkflowResponse>() {});
                    if (response.getResponseCode() == PAWorkflowResponse.ResponseCode.Success)
                    {
                        result = response.getData();
                        logDebug("execute", "Success:" + result);
                    }
                    else
                    {
                        error = response.getError();
                        logError("execute", "Error:" + error);
                    }
                }
                else
                {
                    if (error.isEmpty()) error = "No response received from the workflow function";
                    logError("execute", "Error:  Null reponse from PA");
                }
            }
            catch (Exception ex) 
            {
                logDebug("execute", "Unable to create PAWorkflowResponse, returning raw data");
                
                // Since we cannot parse this return the original response as is
                result = rawResponse;
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            error = "System error: " + e.getMessage();
        }

        results.put("result", result);
        results.put("error", error);

        if (error.isEmpty())
            logInfo("execute", "Processing complete, returning results");
        else
            logError("execute", error);
        
        return  results;
    }
}
