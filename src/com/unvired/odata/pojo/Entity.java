package com.unvired.odata.pojo;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.unvired.odata.workflow.WFODataEntity;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Entity {

    private String entitySet;
    private String entityType;
    private List<String> keys = null;
    private List<Field> fields = null;
    private List<String> navigation = null;

    public String getEntitySet() {
        return entitySet;
    }

    public void setEntitySet(String entitySet) {
        this.entitySet = entitySet;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entity) {
        this.entityType = entity;
    }

    public List<String> getKeys() {
        return keys;
    }

    public void setKeys(List<String> keys) {
        this.keys = keys;
    }

    public List<Field> getFields() {
        return fields;
    }

    public void setFields(List<Field> fields) {
        this.fields = fields;
    }

    public List<String> getNavigation() {
        return navigation;
    }

    public void setNavigation(List<String> navigation) {
        this.navigation = navigation;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(Entity.class.getName()).append('@').append(Integer.toHexString(System.identityHashCode(this))).append('[');
        sb.append("entityset");
        sb.append('=');
        sb.append(((this.entitySet == null)?"<null>":this.entitySet));
        sb.append(',');
        sb.append("entity");
        sb.append('=');
        sb.append(((this.entityType == null)?"<null>":this.entityType));
        sb.append(',');
        sb.append("keys");
        sb.append('=');
        sb.append(((this.keys == null)?"<null>":this.keys));
        sb.append(',');
        sb.append("fields");
        sb.append('=');
        sb.append(((this.fields == null)?"<null>":this.fields));
        sb.append(',');
        sb.append("navigate");
        sb.append('=');
        sb.append(((this.navigation == null)?"<null>":this.navigation));
        sb.append(',');
        if (sb.charAt((sb.length()- 1)) == ',') {
            sb.setCharAt((sb.length()- 1), ']');
        } else {
            sb.append(']');
        }
        return sb.toString();
    }
    
    public WFODataEntity toODataEntity()
    {
        WFODataEntity entity = new WFODataEntity();
        entity.setEntitySet(entitySet);
        entity.setEntityType(entityType);
        for (String key: keys)
            entity.addKey(key);
        for (Field field: fields)
            entity.addField(field.getName(), translateEDMDataType(field.getType()), "", 
                            field.getNullable() != null ? field.getNullable() : true);
        for (String navigate: navigation) {
            entity.addNavigationMeta(navigate, true);
        }
        return entity;
    }
    
    @SuppressWarnings("rawtypes")
    private Class translateEDMDataType(String type)
    {
        Class classType = null;
        if (type.toLowerCase().startsWith("edm.")) type = type.substring(4);
        switch (type.toLowerCase()) {
            case "boolean":
                classType = java.lang.Boolean.class;
                break;
            case "string":
                classType = java.lang.String.class;
                break;
            case "int16":
            case "int32":
            case "int64":
            case "byte":
            case "sbyte":
            case "double":
            case "decimal":
                classType = java.math.BigDecimal.class;
                break;
            case "date":
            case "datetime":
            case "datetimeoffset":
            case "time":
            case "timeofday":
                classType = org.joda.time.DateTime.class;
                break;
            default:
                classType = java.lang.String.class;
                break;
        }
        
        return classType;
    }
}