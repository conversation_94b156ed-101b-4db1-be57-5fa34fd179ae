package com.unvired.api;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.google.api.client.util.Base64;
import com.unvired.common.WorkflowCacheUtils;
import com.unvired.ump.agent.IBusinessProcess;
import com.unvired.ump.agent.ILogger;
import com.unvired.ump.api.IbXmlMessage;
import com.unvired.ump.api.IubPrincipal;

public abstract class AbstractWorkflowRequest
{
    protected IBusinessProcess process = null;
    protected IbXmlMessage ibMessage = null;
    protected ILogger logger = null;
    protected IubPrincipal principal = null;
    protected Map<String, String> envVars;
    
    private String className = null;
    
    public AbstractWorkflowRequest()
    {
    }
    
    public void init(String convId) throws Exception
    {
        process = WorkflowCacheUtils.getBusinessProcess(convId);
        if (process != null) {
            logger = process.getLogger();
            
            ibMessage = WorkflowCacheUtils.getIbXmlMessage(convId);
            principal = ibMessage.getIubPrincipal();
            
            // Set up app log level for this workflow execution thread
            WorkflowCacheUtils.doMDC(ibMessage);
            className = this.getClass().getName();
            
            // Retrieve the environment variables (form attributes)
            envVars = WorkflowCacheUtils.getEnvironmentVar(convId);
        
            logInfo("init", "Init done for: " + convId);
        } else {
            throw new Exception("Fatal Error: Process not found for ConversationID: " + convId);
        }
    }
    
    public void logError(String method, String message)
    {
        logger.addErrorLog(className, method, message);
    }
    
    public void logInfo(String method, String message)
    {
        logger.addInfoLog(className, method, message);
    }
    
    public void logDebug(String method, String message)
    {
        logger.addDebugLog(className, method, message);
    }
    
    protected String substituteEnvironmentVar(String value)
    {
        String regex = "\\$\\{env_[a-zA-Z0-9_-]*\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) 
        {
            // Extract the base64 string and decode it
            String envvar = value.substring(matcher.start()+6, matcher.end()-1);
            if (envVars.containsKey(envvar)) {
                matcher.appendReplacement(sb, Matcher.quoteReplacement(envVars.get(envvar)));
                logDebug("execute", "Replaced: " + envvar + " with environment variable");
            }
            else {
                logError("execute", "Environment variable: " + envvar + " ,value not found for replacement");
            }
            
        };            
        matcher.appendTail(sb);
        return sb.toString();
    }
}
