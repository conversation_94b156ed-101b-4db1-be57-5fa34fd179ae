package com.unvired.common;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import com.unvired.lib.utility.BusinessEntity;
import com.unvired.ump.agent.IBusinessProcess;
import com.unvired.ump.agent.IUMPService;
import com.unvired.ump.api.IbXmlMessage;
import com.unvired.ump.api.IubPrincipal;

@SuppressWarnings({ "rawtypes", "unchecked" })
public class WorkflowCacheUtils
{
    public static IBusinessProcess getBusinessProcess(String convId)
    {
        IBusinessProcess busProc = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getUMPBusinessProcess = wfCache.getMethod("getBusinessProcess", String.class);
            busProc = (IBusinessProcess) getUMPBusinessProcess.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return busProc;
    }
    
    public static IUMPService getUMPService(String convId)
    {
        IBusinessProcess busProc = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getUMPBusinessProcess = wfCache.getMethod("getBusinessProcess", String.class);
            busProc = (IBusinessProcess) getUMPBusinessProcess.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return busProc != null ? busProc.getService() : null;
    }    

    public static IbXmlMessage getIbXmlMessage(String convId)
    {
        IbXmlMessage msg = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getIbxmlMessage = wfCache.getMethod("getIbXmlMessage", String.class);
            msg = (IbXmlMessage) getIbxmlMessage.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return msg;
    }

    public static IubPrincipal getPrincipal(String convId)
    {
        IubPrincipal principal = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getIbxmlMessage = wfCache.getMethod("getIbXmlMessage", String.class);
            IbXmlMessage msg = (IbXmlMessage) getIbxmlMessage.invoke(null, convId);
            principal = msg.getIubPrincipal();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return principal;
    }
    
    public static void putResult(String convId, String result, String error)
    {
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method putResult = wfCache.getMethod("putResult", String.class, String.class, String.class);
            putResult.invoke(null, convId, result, error);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }
    
    public static String transformToJson(List<BusinessEntity> bes)
    {
        String result = null;
        try
        {
            Class crossUtils = Class.forName("com.unvired.ump.utility.CrossUtils");
            Method transformJson = crossUtils.getMethod("transformBEstoSimpleJSON", List.class);
            result = (String) transformJson.invoke(null, bes);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return result;
    }
    
    public static void doMDC(IbXmlMessage ibXmlMessage)
    {
        try
        {
            Class crossUtils = Class.forName("com.unvired.ump.utility.CrossUtils");
            Method domdc = crossUtils.getMethod("doMDC", String.class, String.class, String.class, String.class, String.class);
            domdc.invoke(null, ibXmlMessage.getCompanyalias(), ibXmlMessage.getIubPrincipal().getApplicationName(), ibXmlMessage.getIubPrincipal().getUserMap().getUserName(), ibXmlMessage.getConversationId(), ibXmlMessage.getAppLogLevel());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }
    
    public static String encrypt(String company, String data)
    {
        String encrStr = null;
        try {
            Class wfCache = Class.forName("com.unvired.ump.security");
            Method encrypt = wfCache.getMethod("forceSymmetricEncryptWithBase64Encoding", String.class, String.class);
            encrStr = (String) encrypt.invoke(null, data, company);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encrStr;
    }

    public static String decrypt(String company, String data)
    {
        String decStr = null;
        try {
            Class wfCache = Class.forName("com.unvired.ump.security");
            Method decrypt = wfCache.getMethod("forceSymmetricDecryptWithBase64Decoding", String.class, String.class);
            decStr = (String) decrypt.invoke(null, data, company);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decStr;
    }

    public static Map<String, Object> getVariables(String convId)
    {
        Map<String, Object> variables = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getVars = wfCache.getMethod("getVariables", String.class);
            variables = (Map<String, Object>) getVars.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return variables;
    }
    
    public static Map<String, String> getEnvironmentVar(String convId)
    {
        Map<String, String> variables = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getEnvVar = wfCache.getMethod("getEnvironmentVar", String.class);
            variables = (Map<String, String>) getEnvVar.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return variables;
    }

    public static Map<String, String> getFields(String convId)
    {
        Map<String, String> fields = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getFields = wfCache.getMethod("getFields", String.class);
            fields = (Map<String, String>) getFields.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return fields;
    }

    public static Map<String, String> getDateFieldFormats(String convId)
    {
        Map<String, String> formats = null;
        try
        {
            Class wfCache = Class.forName("com.unvired.ump.workflow.WorkflowCache");
            Method getFormats = wfCache.getMethod("getDateFieldFormats", String.class);
            formats = (Map<String, String>) getFormats.invoke(null, convId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return formats;
    }
    
    public static String executeWorkflowAsync(boolean parallel, String convId, String wfName, String wfNamespace, String flowInput, String formId, String submissionId, String taskId, String formUsers, String formData)
    {
        String conversationId = null;
        try
        {
            Class crossUtils = Class.forName("com.unvired.ump.utility.CrossUtils");
            Method execWf = crossUtils.getMethod("executeWorkflowAsync", Boolean.class, String.class, String.class, String.class, String.class, String.class, String.class, String.class, String.class, String.class, String.class);
            conversationId = (String) execWf.invoke(null, parallel, convId, wfName, wfNamespace, flowInput, formId, submissionId, taskId, formUsers, formData);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        
        return conversationId;
    }
}
