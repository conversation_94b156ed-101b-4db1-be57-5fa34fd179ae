package com.unvired.common;

import java.util.Map;
import com.unvired.jdbc.meta.ParameterMeta;
import com.unvired.jdbc.meta.ProcedureMeta;
import com.unvired.jdbc.proxy.Procedure;
import com.unvired.jdbc.util.JDBCConstants.Database;

public class WFStoredProc extends Procedure
{
    private static final long serialVersionUID = -2550083035127514346L;
    public static final String PARAM_TYPE_SUFFIX = "-INOUTTYPE";
    public static final int PARAM_TYPE_IN = 1;
    public static final int PARAM_TYPE_INOUT = 2;
    public static final int PARAM_TYPE_OUT = 4;
    
    private ProcedureMeta meta;

    public WFStoredProc(String system, Map<String, String> sysProps, Map<String, Object> input, String functionName)
    {
        String dbType = sysProps.get("DB_TYPE");
        Database type = Enum.valueOf(Database.class, dbType);
        
        String schema = sysProps.get("SCHEMA");
        if (schema == null) schema = "";
        
        String catalog = sysProps.get("DATABASE");
        if (catalog == null) catalog = "";
        
        meta = new ProcedureMeta(type, catalog, schema, functionName, 2, "", "com.unvired.common.WFStoredProc");
        for (Map.Entry<String, Object> entry : input.entrySet())
        {
            String key = entry.getKey();
            if (!key.endsWith(PARAM_TYPE_SUFFIX))           // Skip param-type key
            {
                int paramtype = (int) input.get(key + PARAM_TYPE_SUFFIX);   // Get the param type
                meta.addParameter(new ParameterMeta(type,"@" + entry.getKey(), "", 0, "", paramtype, 0, 0));
                if (paramtype != PARAM_TYPE_OUT)        // If not type OUT add the value
                    setValue("@" + entry.getKey(), entry.getValue());
            }
        }
    }
    
    public ProcedureMeta getMetaData()
    {
        return meta;
    }
}
