package com.unvired.common;

import java.util.LinkedHashMap;
import java.util.Map.Entry;
import org.apache.commons.lang3.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.api.client.util.Base64;
import com.unvired.api.AbstractWorkflowRequest;

public class WorkflowUtils
{
    public static String convertInput(AbstractWorkflowRequest rqst, String inputData) throws Exception
    {
        ObjectMapper mapper = new ObjectMapper();
        LinkedHashMap<String, JsonNode> inputs = mapper.readValue(inputData, new TypeReference<LinkedHashMap<String, JsonNode>>() { });
        rqst.logInfo("execute", "Processing inputData for BASE64");

        ObjectNode processedInput = mapper.createObjectNode();
        for (Entry<String, JsonNode> input: inputs.entrySet())
        {
            String key = input.getKey();
            JsonNode value = input.getValue();
            if (value.isTextual()) {
                String txtValue = value.asText();
                if (txtValue.indexOf("BASE64://") != -1) {
                    txtValue = StringUtils.strip(txtValue, "\"");
                    txtValue = txtValue.substring(9);               // Remove the BASE64://
                    txtValue = new String(Base64.decodeBase64(txtValue.getBytes("utf-8")));
                    rqst.logDebug("execute", "inputdata base64 extract: " + txtValue);
                    processedInput.put(key, txtValue);
                } else {
                    processedInput.set(key, value);
                }
            } else {
                processedInput.set(key, value);
            }
        }
        return mapper.writeValueAsString(processedInput);
    }
}
