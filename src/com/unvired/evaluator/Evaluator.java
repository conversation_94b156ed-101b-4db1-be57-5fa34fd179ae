package com.unvired.evaluator;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.Hashtable;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.google.api.client.util.Base64;
import com.google.gson.Gson;
import com.udojava.evalex.AbstractLazyFunction;
import com.udojava.evalex.AbstractLazyOperator;
import com.udojava.evalex.Expression;
import com.unvired.common.WorkflowCacheUtils;
import com.unvired.ump.agent.ILogger;

public class Evaluator extends Expression implements EvaluatorConstants
{
    // Zero or False
    private static LazyNumber ZERO = new LazyNumber() 
    {
        public BigDecimal eval() 
        {
            return BigDecimal.ZERO;
        }
        public String getString() 
        {
            return "0";
        }
    };
    
    // One or True
    private static LazyNumber ONE = new LazyNumber() 
    {
        public BigDecimal eval() 
        {
            return BigDecimal.ONE;
        }         
        public String getString() 
        {
            return "1";
        }
    };
    
    private String company;
    private ILogger logger;
    private boolean trimZeroes = false;
    private Set<String> declaredVars = null;
    private Hashtable<String, ArrayList<Number>> arrayVars = new Hashtable<String, ArrayList<Number>>();
    
    public Evaluator(String expression)
    {
        super(expression);
        this.company = null;
        trimZeroes = !checkIfStringFunction(expression);
    }
    
    public void setLogger(String company, ILogger logger)
    {
        this.company = company;
        this.logger = logger;
    }
    
    public LazyNumber createSafeString(String string)
    {
        return super.createLazyNumber(new BigString(string));
    }
    
    Evaluator addOperatorsAndFunctions(Evaluator processor)
    {
        // **************************************************************** String Operators *****************************************************************
        
        // String contains
        processor.addOperator(new AbstractLazyOperator(CONTAINS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + CONTAINS, "var1:" + var1 + " var2:" + var2);
                
                return (var1.toLowerCase().contains(var2.toLowerCase())) ? ONE : ZERO;
            }
        });
        
        // String does not contain
        processor.addOperator(new AbstractLazyOperator(NOT+CONTAINS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + NOT + CONTAINS, "var1:" + var1 + " var2:" + var2);
                
                return (!var1.toLowerCase().contains(var2.toLowerCase())) ? ONE : ZERO;
            }
        });
        
        // String begins with
        processor.addOperator(new AbstractLazyOperator(BEGINS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + BEGINS, "var1:" + var1 + " var2:" + var2);
                
                return (var1.toLowerCase().startsWith(var2.toLowerCase())) ? ONE : ZERO;
            }
        });


        // String does not begin with
        processor.addOperator(new AbstractLazyOperator(NOT+BEGINS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + NOT + BEGINS, "var1:" + var1 + " var2:" + var2);
                
                return (!var1.toLowerCase().startsWith(var2.toLowerCase())) ? ONE : ZERO;
            }
        });
        
        // String equals
        processor.addOperator(new AbstractLazyOperator(EQUALS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + EQUALS, "var1:" + var1 + " var2:" + var2);
                
                return (var1.equalsIgnoreCase(var2)) ? ONE : ZERO;
            }
        });
        
        // String not equals
        processor.addOperator(new AbstractLazyOperator(NOT+EQUALS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + NOT + EQUALS, "var1:" + var1 + " var2:" + var2);
                
                return (!var1.equalsIgnoreCase(var2)) ? ONE : ZERO;
            }
        });        

        // String in
        processor.addOperator(new AbstractLazyOperator(IN, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + IN, "var1:" + var1 + " var2:" + var2);
                
                // v2 will have a list of comman separated values
                String parts[] = var2.trim().split("\\s*,\\s*");
                for (String each: parts)
                {
                    if (var1.equalsIgnoreCase(each))
                        return ONE;
                }
                
                return ZERO;
            }
        });
        
        // String not in
        processor.addOperator(new AbstractLazyOperator(NOT+IN, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + NOT + IN, "var1:" + var1 + " var2:" + var2);
                
                // v2 will have a list of comman separated values
                String parts[] = var2.trim().split("\\s*,\\s*");
                for (String each: parts)
                {
                    if (var1.equalsIgnoreCase(each))
                        return ZERO;
                }
                
                return ONE;
            }
        });
        
        // String/Date/Time/DateTime is empty
        processor.addOperator(new AbstractLazyOperator(EMPTY, Expression.OPERATOR_PRECEDENCE_UNARY, true, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var = processor.getValueOrVariable(v1.getString());
                logger.addDebugLog("Evaluator", "Operator: " + EMPTY, "var1:" + var);
                return ((var.isEmpty() || var.equals("\"\""))) ? ONE : ZERO;
            }
        });  
        
        // String/Date/Time/DateTime is not empty
        processor.addOperator(new AbstractLazyOperator(NOT+EMPTY, Expression.OPERATOR_PRECEDENCE_UNARY, true, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var = processor.getValueOrVariable(v1.getString());
                logger.addDebugLog("Evaluator", "Operator: " + NOT + EMPTY, "var1:" + var);
                return ((var.isEmpty() || var.equals("\"\""))) ? ZERO : ONE;
            }
        });  

        // String is empty list (either == to "" or "[]"
        processor.addOperator(new AbstractLazyOperator(EMPTY_LIST, Expression.OPERATOR_PRECEDENCE_UNARY, true, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String val = processor.getValueOrVariable(v1.getString());
                logger.addDebugLog("Evaluator", "Operator: " + EMPTY_LIST, "var1:" + val);

                if (val.isEmpty() || val.equals("\"\"") || val.equals("[]") || val.equals("[\"\"]"))
                    return ONE;
                
                return ZERO;
            }
        });  
        
        // String is not empty list
        processor.addOperator(new AbstractLazyOperator(NOT+EMPTY_LIST, Expression.OPERATOR_PRECEDENCE_UNARY, true, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String val = processor.getValueOrVariable(v1.getString());
                logger.addDebugLog("Evaluator", "Operator: " + NOT + EMPTY_LIST, "var1:" + val);
                
                if (val.isEmpty() || val.equals("\"\"") || val.equals("[]") || val.equals("[\"\"]"))
                    return ZERO;
                
                return ONE;
            }
        }); 
        
        // String/Date/Time/DateTime is null
        processor.addOperator(new AbstractLazyOperator(NULL, Expression.OPERATOR_PRECEDENCE_UNARY, true, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var = processor.getValueOrVariable(v1.getString());
                return (var == null) ? ONE : ZERO;
            }
        });  
        
        // String/Date/Time/DateTime is not null
        processor.addOperator(new AbstractLazyOperator(NOT+NULL, Expression.OPERATOR_PRECEDENCE_UNARY, true, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                String var = processor.getValueOrVariable(v1.getString());
                return (var != null) ? ONE : ZERO;
            }
        });  
        
        // Date equals
        processor.addOperator(new AbstractLazyOperator(DATE_PREFIX+EQUALS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                LazyNumber result = ZERO;
                
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + DATE_PREFIX + EQUALS, "var1:" + var1 + " var2:" + var2);
                
                try
                {
                    String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                    SimpleDateFormat sdf = new SimpleDateFormat(format);

                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    
                    result = date1.equals(date2) ? ONE : ZERO;
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                
                return result;
            }
        });
        
        // Date not equals
        processor.addOperator(new AbstractLazyOperator(DATE_PREFIX+NOT+EQUALS, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                LazyNumber result = ZERO;
                
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + DATE_PREFIX + NOT + EQUALS, "var1:" + var1 + " var2:" + var2);
                
                try
                {
                    String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                    SimpleDateFormat sdf = new SimpleDateFormat(format);

                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    
                    result = date1.equals(date2) ? ZERO : ONE;
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                
                return result;
            }
        });   
        
        // Date lesser
        processor.addOperator(new AbstractLazyOperator(DATE_PREFIX+DATE_LT, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                LazyNumber result = ZERO;
                
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + DATE_PREFIX + DATE_LT, "var1:" + var1 + " var2:" + var2);
                
                try
                {
                    String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                    SimpleDateFormat sdf = new SimpleDateFormat(format);

                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    
                    result = date1.before(date2) ? ONE : ZERO;
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                
                return result;
            }
        });   
        
        
        // Date lesser or equal
        processor.addOperator(new AbstractLazyOperator(DATE_PREFIX+DATE_LE, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                LazyNumber result = ZERO;
                
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + DATE_PREFIX + DATE_LE, "var1:" + var1 + " var2:" + var2);
                
                try
                {
                    String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                    SimpleDateFormat sdf = new SimpleDateFormat(format);

                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    
                    result = (date1.before(date2)  || date1.equals(date2)) ? ONE : ZERO;
                } 
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                
                return result;
            }
        });    
        
        // Date greater
        processor.addOperator(new AbstractLazyOperator(DATE_PREFIX+DATE_GT, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                LazyNumber result = ZERO;
                
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + DATE_PREFIX + DATE_LT, "var1:" + var1 + " var2:" + var2);
                
                try
                {
                    String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                    SimpleDateFormat sdf = new SimpleDateFormat(format);

                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    
                    result = date1.after(date2) ? ONE : ZERO;
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                
                return result;
            }
        });   
        
        
        // Date greater or equal
        processor.addOperator(new AbstractLazyOperator(DATE_PREFIX+DATE_GE, Expression.OPERATOR_PRECEDENCE_COMPARISON, true, true) 
        {
            @Override
            public LazyNumber eval(LazyNumber v1, LazyNumber v2)
            {
                LazyNumber result = ZERO;
                
                String var1 = processor.getValueOrVariable(v1.getString());
                String var2 = processor.getValueOrVariable(v2.getString());
                
                logger.addDebugLog("Evaluator", "Operator: " + DATE_PREFIX + DATE_LE, "var1:" + var1 + " var2:" + var2);
                
                try
                {
                    String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                    SimpleDateFormat sdf = new SimpleDateFormat(format);

                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    
                    result = (date1.after(date2)  || date1.equals(date2)) ? ONE : ZERO;
                } 
                catch (Exception e)
                {
                    e.printStackTrace();
                }
                
                return result;
            }
        });           
        
        // **************************************************************** String Functions *****************************************************************
        
        // Assignment function to just return the input string
        processor.addLazyFunction(new AbstractLazyFunction(ASSIGN, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                logger.addDebugLog("Evaluator", "Operator: " + ASSIGN, "param:" + lazyParams.get(0).getString());
                return processor.createLazyNumber(new BigString(processor.getValueOrVariable(lazyParams.get(0).getString())));
            }
        }); 

        // Convert string to number
        processor.addLazyFunction(new AbstractLazyFunction(TO_NUMBER, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                String input = lazyParams.get(0).getString();
                logger.addDebugLog("Evaluator", "Operator: " + TO_NUMBER, "param:" + input);
                
                LazyNumber result = null;
                try {
                    double d = Double.parseDouble(input);
                    if ((int) d == d)
                        result = processor.createLazyNumber(new BigDecimal((int) d));
                    else
                        result = processor.createLazyNumber(new BigDecimal(d));
                        
                } catch (Exception e) {
                    logger.addErrorLog("Evaluator", "Operator: " + TO_NUMBER , "error parsing number");
                }
                return result;
            }
        }); 

        // Concat the given number of strings
        processor.addLazyFunction(new AbstractLazyFunction(CONCAT, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                StringBuilder bldr = new StringBuilder();
                for (LazyNumber lazyParam: lazyParams)
                {
                    logger.addDebugLog("Evaluator", "Operator: " + CONCAT, "param:" + lazyParam.getString());
                    bldr.append(processor.getValueOrVariable(lazyParam.getString()));
                }
                LazyNumber result = processor.createLazyNumber(new BigString(bldr.toString()));
                return result;
            }
        }); 
        
        // Substring of the given string
        processor.addLazyFunction(new AbstractLazyFunction(SUBSTRING, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;
                int numItems = lazyParams.size();
                if (numItems < 2)
                    throw new ExpressionException("substring requires beginIndex and optionally endIndex to be executed");
                
                int beginIndex = Integer.parseInt(lazyParams.get(1).getString());
                
                String var = processor.getValueOrVariable(lazyParams.get(0).getString());
                logger.addDebugLog("Evaluator", "Operator: " + SUBSTRING, "var1:" + var + " beginIndex:" + beginIndex);
                
                String resultStr = "";
                if (numItems == 2)
                {
                    // Ignore exceptions and return an empty string
                    try {
                        resultStr = var.substring(beginIndex);
                    } catch (Exception e) { e.printStackTrace();}
                    result = processor.createLazyNumber(new BigString(resultStr));
                }
                else
                {
                    int endIndex = Integer.parseInt(lazyParams.get(2).getString());
                    logger.addDebugLog("Evaluator", "Operator: " + SUBSTRING, "var1:" + var + " beginIndex:" + beginIndex + " endIndex:" + endIndex);
                    
                    // Ignore exceptions and return an empty string
                    try {
                        resultStr = var.substring(beginIndex, endIndex);
                        logger.addDebugLog("Evaluator", "Operator: " + SUBSTRING, "Computed substring: " + resultStr);
                    } catch (Exception e) { e.printStackTrace();}
                    result = processor.createLazyNumber(new BigString(resultStr));
                }
                return result;
            }
        }); 
        
        // toUpper of the given string
        processor.addLazyFunction(new AbstractLazyFunction(UPPER, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                logger.addDebugLog("Evaluator", "Operator: " + UPPER, "var1:" + lazyParams.toString());
                return processor.createLazyNumber(new BigString(processor.getValueOrVariable(lazyParams.get(0).getString()).toUpperCase()));
            }
        });   
        
        // toLower of the given string
        processor.addLazyFunction(new AbstractLazyFunction(LOWER, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                logger.addDebugLog("Evaluator", "Operator: " + LOWER, "var1:" + lazyParams.toString());
                return processor.createLazyNumber(new BigString(processor.getValueOrVariable(lazyParams.get(0).getString()).toLowerCase()));
            }
        });        
        
        // trim of the given string
        processor.addLazyFunction(new AbstractLazyFunction(TRIM, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                logger.addDebugLog("Evaluator", "Operator: " + TRIM, "var1:" + lazyParams.toString());
                return processor.createLazyNumber(new BigString(processor.getValueOrVariable(lazyParams.get(0).getString()).trim()));
            }
        });        

        // index of the given string
        processor.addLazyFunction(new AbstractLazyFunction(INDEXOF, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                int numItems = lazyParams.size();
                if (numItems < 2)
                    throw new ExpressionException("indexof requires the input string, string to find and optionally startIndex to be executed");
                
                logger.addDebugLog("Evaluator", "Operator: " + INDEXOF, "params" + lazyParams.toString());
                int startIndex = 0;
                if (numItems >= 3)
                    startIndex = Integer.parseInt(lazyParams.get(2).getString());
                
                return processor.createLazyNumber(new BigDecimal(lazyParams.get(0).getString().indexOf(lazyParams.get(1).getString(), startIndex)));
            }
        });  
        
        // length of the given string
        processor.addLazyFunction(new AbstractLazyFunction(LENGTH, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                int length = lazyParams.get(0).getString().length();
                logger.addDebugLog("Evaluator", "Operator: " + LENGTH, "params" + lazyParams.get(0).getString() + ", length: " + length);
                return processor.createLazyNumber(new BigDecimal(length));
            }
        });  
        
        // Replace of the given string
        processor.addLazyFunction(new AbstractLazyFunction(REPLACE, 3, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;
                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String searchStr = processor.getValueOrVariable(lazyParams.get(1).getString());
                String replaceStr = processor.getValueOrVariable(lazyParams.get(2).getString());
                logger.addDebugLog("Evaluator", "Operator: " + REPLACE, "string:" + origStr + " Search:" + searchStr + " Replace:" + replaceStr);
                result = processor.createLazyNumber(new BigString(origStr.replace(searchStr, replaceStr)));
                return result;
            }
        }); 

        // Replace regex of the given string
        processor.addLazyFunction(new AbstractLazyFunction(REPLACE_REGEX, 3, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;
                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String regex = processor.getValueOrVariable(lazyParams.get(1).getString());
                String replaceStr = processor.getValueOrVariable(lazyParams.get(2).getString());
                logger.addDebugLog("Evaluator", "Operator: " + REPLACE_REGEX, "string:" + origStr + " Search:" + regex + " Replace:" + replaceStr);
                result = processor.createLazyNumber(new BigString(origStr.replaceAll(regex, replaceStr)));
                return result;
            }
        }); 
        
        // Search of the given string, returns an array of all index that match
        processor.addLazyFunction(new AbstractLazyFunction(SEARCH, 2, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;
                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String searchStr = processor.getValueOrVariable(lazyParams.get(1).getString());
                logger.addDebugLog("Evaluator", "Operator: " + SEARCH, "string:" + origStr + " Search:" + searchStr);
                
                ArrayList<Integer> indexes = new ArrayList<Integer>();
                int index = 0;
                while(index != -1){
                    index = origStr.indexOf(searchStr, index);
                    if (index != -1) {
                        indexes.add(index);
                        index++;
                    }
                }
                
                // Return a JSOn array of indexes
                result = processor.createLazyNumber(new BigString(new Gson().toJson(indexes)));
                return result;
            }
        });         

        // Search regex of the given string, returns an array of all matched strings
        processor.addLazyFunction(new AbstractLazyFunction(SEARCH_REGEX, 2, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;
                ArrayList<String> strings = new ArrayList<String>();
                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String regex = processor.getValueOrVariable(lazyParams.get(1).getString());
                logger.addDebugLog("Evaluator", "Operator: " + SEARCH_REGEX, "string:" + origStr + " Regex:" + regex);
                
                Matcher matcher = Pattern.compile(regex).matcher(origStr);
                while (matcher.find()) 
                {
                    strings.add(matcher.group());
                };            
                
                // Return a JSOn array of indexes
                result = processor.createLazyNumber(new BigString(new Gson().toJson(strings)));
                return result;
            }
        });
        
        // Split of the given string, returns an array of all split strings
        processor.addLazyFunction(new AbstractLazyFunction(SPLIT, 2, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;
                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String delimiter = processor.getValueOrVariable(lazyParams.get(1).getString());
                logger.addDebugLog("Evaluator", "Operator: " + SPLIT, "string:" + origStr + " Delimiter:" + delimiter);

                String split[] = origStr.split(delimiter);
                // Return a JSOn array of indexes
                result = processor.createLazyNumber(new BigString(new Gson().toJson(split)));
                return result;
            }
        });
        
        // base64 encoded string
        processor.addLazyFunction(new AbstractLazyFunction(BASE64_ENCODE, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;

                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                logger.addDebugLog("Evaluator", "Operator: " + BASE64_ENCODE, "string:" + origStr);

                // Return BASE64
                try {
                    result = processor.createLazyNumber(new BigString(new String(Base64.encodeBase64(origStr.getBytes("utf-8")))));
                } catch(Exception e) {}
                return result;
            }
        });

        // base64 decode string
        processor.addLazyFunction(new AbstractLazyFunction(BASE64_DECODE, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;

                String base64Str = processor.getValueOrVariable(lazyParams.get(0).getString());
                logger.addDebugLog("Evaluator", "Operator: " + BASE64_DECODE, "string:" + base64Str);

                // Return decoded BASE64
                try {
                    result = processor.createLazyNumber(new BigString(new String(Base64.decodeBase64(base64Str.getBytes("utf-8")))));
                } catch(Exception e) {}
                return result;
            }
        });

        // encrypt string
        processor.addLazyFunction(new AbstractLazyFunction(ENCRYPT, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;

                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String encrStr = WorkflowCacheUtils.encrypt(company, origStr);
                if (encrStr != null)
                    result = processor.createLazyNumber(new BigString(encrStr));
                return result;
            }
        });

        // decrypt string
        processor.addLazyFunction(new AbstractLazyFunction(DECRYPT, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                LazyNumber result = null;

                String origStr = processor.getValueOrVariable(lazyParams.get(0).getString());
                String decStr = WorkflowCacheUtils.decrypt(company, origStr);
                if (decStr != null)
                    result = processor.createLazyNumber(new BigString(decStr));
                return result;
            }
        });

        // ***************************************************************** Math Functions *****************************************************************
        
        // Convert number to string
        processor.addLazyFunction(new AbstractLazyFunction(TO_STRING, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                String input = lazyParams.get(0).getString();
                logger.addDebugLog("Evaluator", "Operator: " + TO_STRING, "param:" + input);
                
                return processor.createLazyNumber(new BigString(input));
            }
        }); 

        
        // Add all numbers in an array
        processor.addLazyFunction(new AbstractLazyFunction(SUM, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                String variable = lazyParams.get(0).getString();
                if (!processor.arrayVars.containsKey(variable))
                    throw new ExpressionException("Insufficient parameters: Array of values for variable \"" + variable + "\" not found");
                
                double sum = 0.0;
                ArrayList<Number> values = processor.arrayVars.get(variable);
                for (Number value: values)
                    sum += value.doubleValue();
                LazyNumber result = processor.createLazyNumber(new BigDecimal(sum));
                return result;
            }
        });        

        // Average all numbers in an array
        processor.addLazyFunction(new AbstractLazyFunction(AVERAGE, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                String variable = lazyParams.get(0).getString();
                if (!processor.arrayVars.containsKey(variable))
                    throw new ExpressionException("Insufficient parameters: Array of values for variable \"" + variable + "\" not found");
                
                double sum = 0.0;
                ArrayList<Number> values = processor.arrayVars.get(variable);
                for (Number value: values)
                    sum += value.doubleValue();
                LazyNumber result = processor.createLazyNumber(new BigDecimal(sum/values.size()));
                return result;
            }
        });

        // Min of all numbers in an array
        processor.addLazyFunction(new AbstractLazyFunction(MIN, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                String variable = lazyParams.get(0).getString();
                if (!processor.arrayVars.containsKey(variable))
                    throw new ExpressionException("Insufficient parameters: Array of values for variable \"" + variable + "\" not found");

                ArrayList<Number> values = processor.arrayVars.get(variable);
                double min = values.get(0).doubleValue();
                for (Number value: values) {
                    if (min > value.doubleValue())
                        min = value.doubleValue();
                }
                LazyNumber result = processor.createLazyNumber(new BigDecimal(min));
                return result;
            }
        }); 
        
        // Max of all numbers in an array
        processor.addLazyFunction(new AbstractLazyFunction(MAX, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                String variable = lazyParams.get(0).getString();
                if (!processor.arrayVars.containsKey(variable))
                    throw new ExpressionException("Insufficient parameters: Array of values for variable \"" + variable + "\" not found");

                ArrayList<Number> values = processor.arrayVars.get(variable);
                double max = values.get(0).doubleValue();
                for (Number value: values) {
                    if (max < value.doubleValue())
                        max = value.doubleValue();
                }
                LazyNumber result = processor.createLazyNumber(new BigDecimal(max));
                return result;
            }
        }); 

        // Truncate the decimal part of the number
        processor.addLazyFunction(new AbstractLazyFunction(TRUNCATE, 1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                double num = Double.parseDouble(lazyParams.get(0).getString());
                return (processor.createLazyNumber(new BigDecimal((int) num)));
            }
        });        

        // Round a float or double to the required number of decimal places
        processor.addLazyFunction(new AbstractLazyFunction(ROUND, 2, false) 
        {
            @SuppressWarnings("deprecation")
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                double num = Double.parseDouble(lazyParams.get(0).getString());
                int decimals = Integer.parseInt(lazyParams.get(1).getString());
                return processor.createLazyNumber(new BigDecimal(num).setScale(decimals, BigDecimal.ROUND_HALF_UP));
            }
        });
        
        // ***************************************************************** Date Functions *****************************************************************
        
        // Parse a date
        processor.addLazyFunction(new AbstractLazyFunction(DATE_PARSE, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                if (lazyParams.size() < 2)
                    throw new ExpressionException("Date parse requires the date, output format desired and optionally date format to parse");
                    
                String informat = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                if (lazyParams.size() > 2)
                    informat = processor.getValueOrVariable(lazyParams.get(2).getString()).trim();
                
                String var = processor.getValueOrVariable(lazyParams.get(0).getString()).trim();
                String outformat = processor.getValueOrVariable(lazyParams.get(1).getString()).trim();
                String dateStr = "";
                try
                {
                    SimpleDateFormat sdf = new SimpleDateFormat(informat);
                    Date date = sdf.parse(var);
                    dateStr = new SimpleDateFormat(outformat).format(date);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                    throw new ExpressionException("Error parsing date: " + var + " using format: " + informat);
                }
                
                return processor.createLazyNumber(new BigString(dateStr));
            }
        });          
        // Add two dates
        processor.addLazyFunction(new AbstractLazyFunction(DATE_ADD, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                if (lazyParams.size() < 2)
                    throw new ExpressionException("Date add requires two dates and optionally date format to parse");
                    
                String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                if (lazyParams.size() == 3)
                    format = processor.getValueOrVariable(lazyParams.get(2).getString()).trim();
                
                String var1 = processor.getValueOrVariable(lazyParams.get(0).getString()).trim();
                String var2 = processor.getValueOrVariable(lazyParams.get(1).getString()).trim();
                
                long dateSum = 0L;
                
                try
                {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    dateSum = date1.getTime() + date2.getTime();
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                    throw new ExpressionException("Error parsing date: " + var1 + " or " + var2 + " using format: " + format);
                }
                
                return processor.createLazyNumber(new BigString(new Date(dateSum).toString()));
            }
        });      
        
        // Difference of two dates
        processor.addLazyFunction(new AbstractLazyFunction(DATE_DIFF, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                if (lazyParams.size() < 2)
                    throw new ExpressionException("Date difference requires two dates and optionally date format to parse");
                    
                String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                if (lazyParams.size() == 3)
                    format = processor.getValueOrVariable(lazyParams.get(2).getString()).trim();
                
                String var1 = processor.getValueOrVariable(lazyParams.get(0).getString()).trim();
                String var2 = processor.getValueOrVariable(lazyParams.get(1).getString()).trim();
                
                long dateDiff = 0L;
                
                try
                {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    Date date1 = sdf.parse(var1);
                    Date date2 = sdf.parse(var2);
                    dateDiff = date1.getTime() - date2.getTime();
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                    throw new ExpressionException("Error parsing date: " + var1 + " or " + var2 + " using format: " + format);
                }
                
                return processor.createLazyNumber(new BigString(new Date(dateDiff).toString()));
            }
        });
        
        // Advance date by number of days
        processor.addLazyFunction(new AbstractLazyFunction(DATE_ADVANCE, -1, false) 
        {
            @Override
            public LazyNumber lazyEval(List<LazyNumber> lazyParams) 
            {
                if (lazyParams.size() < 3)
                    throw new ExpressionException("Date advance requires a date, advance by and number of days and optionally date format to parse");
                    
                String format = "yyyy-MM-dd'T'HH:mm:ssX";           // The default JS date format 
                if (lazyParams.size() == 4)
                    format = processor.getValueOrVariable(lazyParams.get(2).getString()).trim();

                String var1 = processor.getValueOrVariable(lazyParams.get(0).getString()).trim();
                String var2 = processor.getValueOrVariable(lazyParams.get(1).getString()).trim();
                String var3 = processor.getValueOrVariable(lazyParams.get(2).getString()).trim();
                
                String newDate = "";
                
                try
                {
                    SimpleDateFormat sdf = new SimpleDateFormat(format);
                    Date date = sdf.parse(var1);
                    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                    
                    int advanceBy = Integer.parseInt(var2);
                    if (advanceBy > 0)
                    {
                        if (var3.equals("days"))
                            localDateTime = localDateTime.plusDays(advanceBy);
                        else if (var3.equals("weeks"))
                            localDateTime = localDateTime.plusWeeks(advanceBy);
                        else if (var3.equals("months"))
                            localDateTime = localDateTime.plusMonths(advanceBy);
                        else if (var3.equals("years"))
                            localDateTime = localDateTime.plusYears(advanceBy);
                        else if (var3.equals("hours"))
                            localDateTime = localDateTime.plusHours(advanceBy);
                        else if (var3.equals("minutes"))
                            localDateTime = localDateTime.plusMinutes(advanceBy);
                        else if (var3.equals("seconds"))
                            localDateTime = localDateTime.plusSeconds(advanceBy);
                    }
                    else if (advanceBy < 0)
                    {
                        if (var3.equals("days"))
                            localDateTime = localDateTime.minusDays(advanceBy);
                        else if (var3.equals("weeks"))
                            localDateTime = localDateTime.minusWeeks(advanceBy);
                        else if (var3.equals("months"))
                            localDateTime = localDateTime.minusMonths(advanceBy);
                        else if (var3.equals("years"))
                            localDateTime = localDateTime.minusYears(advanceBy);
                        else if (var3.equals("hours"))
                            localDateTime = localDateTime.minusHours(advanceBy);
                        else if (var3.equals("minutes"))
                            localDateTime = localDateTime.minusMinutes(advanceBy);
                        else if (var3.equals("seconds"))
                            localDateTime = localDateTime.minusSeconds(advanceBy);
                    }
                    newDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()).toString();
                }
                catch (ParseException e)
                {
                    e.printStackTrace();
                    throw new ExpressionException("Error parsing date: " + var1);
                }
                catch (NumberFormatException ex)
                {
                    ex.printStackTrace();
                    throw new ExpressionException("Error parsing days to advance by: " + var1);
                }
                return processor.createLazyNumber(new BigString(newDate.toString()));
            }
        });          
        
        return processor;
    }
    
    private boolean checkIfStringFunction(String expression)
    {
        return (expression.contains(CONCAT+OPEN_PAREN) || expression.contains(TRIM+OPEN_PAREN) || 
                expression.contains(UPPER+OPEN_PAREN) || expression.contains(LOWER+OPEN_PAREN) || 
                expression.contains(SUBSTRING+OPEN_PAREN) || expression.contains(INDEXOF+OPEN_PAREN) || 
                expression.contains(SPLIT+OPEN_PAREN) || expression.contains(ASSIGN+OPEN_PAREN) ||
                expression.contains(REPLACE+OPEN_PAREN) || expression.contains(REPLACE_REGEX+OPEN_PAREN) ||
                expression.contains(SEARCH+OPEN_PAREN) || expression.contains(SEARCH_REGEX+OPEN_PAREN) ||
                expression.contains(ENCRYPT+OPEN_PAREN) || expression.contains(DECRYPT+OPEN_PAREN) ||
                expression.contains(BASE64_ENCODE+OPEN_PAREN) || expression.contains(BASE64_DECODE+OPEN_PAREN) ||
                expression.contains(TO_NUMBER+OPEN_PAREN)
                );
    }
    
    public Evaluator with(String name, ArrayList<Number> arrayVal)
    {
        arrayVars.put(name, arrayVal);
        return this;
    }

    public Evaluator and(String name, ArrayList<Number> arrayVal)
    {
        arrayVars.put(name, arrayVal);
        return this;
    }

    // Checks if the given parameter is a variable used in this expression
    private boolean isVariable(String in)
    {
        if (declaredVars == null)
            declaredVars = getDeclaredVariables();
        
        return declaredVars.contains(in);
    }

    // Gets the value given parameter from the list of variables
    private String getValueOrVariable(String in)
    {
        String value = in;
        
        if (value != null) { 
            if (isVariable(in))
            {
                LazyNumber varValue = variables.get(in);
                if (varValue != null)
                    value = varValue.getString();
            }
        }
        
        return value;
    }
    
    public boolean isTrimZeroes()
    {
        return trimZeroes;
    }    
}
