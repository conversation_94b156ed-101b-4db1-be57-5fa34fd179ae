package com.unvired.evaluator;

public interface EvaluatorConstants
{
    static final String CONDITION = "condition"; 
    static final String RULES = "rules";
    
    static final String FIELD = "field";
    static final String OPERATOR = "operator";
    static final String FORMAT = "format";
    static final String VALUE = "value";
    static final String TYPE = "type";

    static final String AND_CONDITION = "and";
    static final String OR_CONDITION = "or";

    static final String SPACE = " ";
    static final String OPEN_PAREN = "(";
    static final String CLOSE_PAREN = ")";
    static final String DBL_QUOTE = "\"";
    
    static final String NOT = "not";
    static final String AND = " && ";
    static final String OR = " || ";
    static final String LE = " <= ";
    static final String GE = " >= ";
    static final String LT = " < ";
    static final String GT = " > ";
    static final String EQ = " == ";
    static final String NE = " != ";
    
    static final String ASSIGN = "assign";
    static final String CONCAT = "concat";
    static final String TRIM = "trim";
    static final String UPPER = "upper";
    static final String LOWER = "lower";
    static final String SUBSTRING = "substring";
    static final String SPLIT = "split";
    static final String LENGTH = "length";
    static final String INDEXOF = "indexof";
    static final String REPLACE = "replace";
    static final String REPLACE_REGEX = "replaceregex";
    static final String SEARCH = "search";
    static final String SEARCH_REGEX = "searchregex";
    static final String BASE64_ENCODE = "base64encode";
    static final String BASE64_DECODE = "base64decode";
    static final String ENCRYPT = "encrypt";
    static final String DECRYPT = "decrypt";
    
    static final String TO_NUMBER = "toNumber";
    static final String TO_STRING = "toString";
    
    static final String DATE_PARSE = "parsedate";
    static final String DATE_ADD = "adddates";
    static final String DATE_DIFF = "diffdates";
    static final String DATE_ADVANCE = "advancedate";
    
    static final String SUM = "sum";
    static final String ROUND = "round";
    static final String TRUNCATE = "truncate";
    static final String AVERAGE = "average";
    static final String MIN = "min";
    static final String MAX = "max";
    
    static final String DATE_PREFIX = "date_";
    
    static final String BEGINS = "beginsWith";
    static final String ENDS = "endsWith";
    static final String EMPTY = "isEmpty";
    static final String EMPTY_LIST = "isEmptyList";
    static final String NULL = "isNull";
    static final String CONTAINS = "contains";
    static final String DOES_NOT_CONTAIN = "does not contain";
    static final String BETWEEN = "between";
    static final String NOT_BETWEEN = "not between";
    static final String GREATER = "greater than";
    static final String GREATER_OR_EQUAL = "greater than or equals";
    static final String LESSER = "less than";
    static final String LESSER_OR_EQUAL = "less than or equals";
    static final String EQUALS = "equals";
    static final String NOT_EQUALS = "not equals";
    static final String IN = "in";
    static final String NOT_IN = "not in";
    static final String BEGINS_WITH = "begins with";
    static final String DOES_NOT_BEGIN_WITH = "does not begin with";
    static final String ENDS_WITH = "ends with";
    static final String DOES_NOT_END_WITH = "does not end with";
    static final String IS_EMPTY = "is empty";
    static final String IS_NOT_EMPTY = "is not empty";
    static final String IS_NULL = "is null";
    static final String IS_NOT_NULL = "is not null";
    static final String IS_EMPTY_LIST = "is empty list";
    static final String IS_NOT_EMPTY_LIST = "is not empty list";
        
    static final String DATE_GT = "Greater";
    static final String DATE_GE = "GreaterOrEqual";
    static final String DATE_LT = "Less";
    static final String DATE_LE = "LessOrEqual";
    
}
