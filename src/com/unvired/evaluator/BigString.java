package com.unvired.evaluator;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;

public class BigString extends BigDecimal
{
    private static final long serialVersionUID = 6179480913911382826L;
    
    private String bigString = null;

    public BigString(char[] in)
    {
        super(in);
        bigString = new String(in);
    }

    public BigString(String val)
    {
        super(1);
        bigString = new String(val);
    }

    public BigString(double val)
    {
        super(val);
        bigString = String.valueOf(val);
    }

    public BigString(BigInteger val)
    {
        super(val);
        bigString = String.valueOf(val);
    }

    public BigString(int val)
    {
        super(val);
        bigString = String.valueOf(val);
    }

    public BigString(long val)
    {
        super(val);
        bigString = String.valueOf(val);
    }

    public BigString(char[] in, MathContext mc)
    {
        super(in, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(String val, MathContext mc)
    {
        super(val, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(double val, MathContext mc)
    {
        super(val, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(BigInteger val, MathContext mc)
    {
        super(val, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(BigInteger unscaledVal, int scale)
    {
        super(unscaledVal, scale);
        throw new UnsupportedOperationException();
    }

    public BigString(int val, MathContext mc)
    {
        super(val, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(long val, MathContext mc)
    {
        super(val, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(char[] in, int offset, int len)
    {
        super(in, offset, len);
        bigString = new String(in);
        bigString = bigString.substring(offset, offset+len);
    }

    public BigString(BigInteger unscaledVal, int scale, MathContext mc)
    {
        super(unscaledVal, scale, mc);
        throw new UnsupportedOperationException();
    }

    public BigString(char[] in, int offset, int len, MathContext mc)
    {
        super(in, offset, len, mc);
        throw new UnsupportedOperationException();
    }
    
    @Override
    public String toString()
    {
        return new String(bigString);
    }
    
    @Override
    public String toPlainString()
    {
        return new String(bigString);
    }
    
    @Override
    public String toEngineeringString()
    {
        return new String(bigString);
    }
}
