package com.unvired.evaluator;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.udojava.evalex.Expression.ExpressionException;
import com.unvired.common.WorkflowCacheUtils;
import com.unvired.ump.agent.ILogger;

public class EvaluatorBuilder implements EvaluatorConstants
{
    private Map<String, String> varTypes = null;
    private Map<String, String> cachedVarTypes = new HashMap<String, String>();
    private ILogger logger = null;

    public EvaluatorBuilder()
    {
    }
    
    public Evaluator buildFromJSON(String company, String conversationId, String jsonExpression, ILogger logger) throws Exception
    {
        this.logger = logger;
        varTypes = WorkflowCacheUtils.getFields(conversationId);
        
        // Need to add the var types from previous calculations
        Map<String, Object> varsInCache = WorkflowCacheUtils.getVariables(conversationId);
        for (Entry<String, Object> entry : varsInCache.entrySet()) {
            Object val = entry.getValue();
            if (val != null) {
                if (val instanceof BigDecimal)
                    cachedVarTypes.put(entry.getKey(), "number");
                else
                    cachedVarTypes.put(entry.getKey(), "string");
            }
        }
                
        ObjectMapper mapper = new ObjectMapper();
        Map<String, JsonNode> map = mapper.readValue(jsonExpression, new TypeReference<LinkedHashMap<String, JsonNode>>() {});

        String expression = getExpression(map.get(CONDITION).asText(), map.get(RULES));
        expression = removeVariablePlaceHolder(expression);

        final Evaluator processor = new Evaluator(expression);
        processor.setLogger(company, logger);
        processor.setVariableCharacters("-_.");
        
        logger.addInfoLog("EvaluatorBuilder", "buildFromJSON", "Created expression from Json");
        
        return processor.addOperatorsAndFunctions(processor);
    }
    
    public Evaluator buildFromExpression(String company, String conversationId, String expression, ILogger logger) throws Exception
    {
        this.logger = logger;
        varTypes = WorkflowCacheUtils.getFields(conversationId);
        
        expression = removeVariablePlaceHolder(expression);
        
        // Are we having a string literal?  If so explicitly turn it into a call to assign
        if (expression.startsWith("\"") && expression.endsWith("\"")) {
            expression = "assign(" + expression + ")";
        }
        
        final Evaluator processor = new Evaluator(expression);
        processor.setLogger(company, logger);
        processor.setVariableCharacters("-_.");
        
        logger.addInfoLog("EvaluatorBuilder", "buildFromExpression", "Created expression from provided expression statement");
        return processor.addOperatorsAndFunctions(processor);
    }    
    
    // Recursive method to extract the expression from the Json representation 
    private String getExpression(String key, JsonNode node) throws Exception
    {
        String expression = "";
        ObjectMapper mapper = new ObjectMapper();
        
        String condition = null;
        if (key.equalsIgnoreCase(AND_CONDITION))
            condition = AND;
        else if (key.equalsIgnoreCase(OR_CONDITION))
            condition = OR;
        else
            throw new ExpressionException("Unsupported condition: " + key);
        
        ArrayList<JsonNode> nodes =  mapper.readValue(node.toString(), new TypeReference<ArrayList<JsonNode>>() {});
        for (int i=0; i< nodes.size(); i++)
        {
            JsonNode thisNode = nodes.get(i);
            
            // What kind of a node is this?  If its an individual rules node then we stringify it else call again
            if (aRule(thisNode))
            {
                expression += expandRule(thisNode);
                if (i < nodes.size()-1)
                    expression += condition;
            }
            else
            {
                Map<String, JsonNode> nextNode =  mapper.readValue(thisNode.toString(), new TypeReference<LinkedHashMap<String, JsonNode>>() {});
                expression += OPEN_PAREN + getExpression(nextNode.get(CONDITION).asText(), nextNode.get(RULES)) + CLOSE_PAREN;
                if (i < nodes.size()-1)
                    expression += condition;
            }
        }
        
        return expression;
    }
    
    // Expands each of the operators to the required expression that can be evaluated
    private String expandRule(JsonNode node) throws Exception
    {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> thisNode =  mapper.readValue(node.toString(), new TypeReference<LinkedHashMap<String, Object>>() {});
        
        String field = thisNode.get(FIELD).toString(); 
        String expression = OPEN_PAREN;
        expression += field;
        
        // Check after removing ${} as handle differently for calc and condition
        String type = "", fldToCheck = removeVariablePlaceHolder(field);
        if (varTypes != null && varTypes.containsKey(fldToCheck))
            type = varTypes.get(fldToCheck);
        else if (cachedVarTypes.containsKey(fldToCheck))
            type = cachedVarTypes.get(fldToCheck);
                
        logger.addDebugLog("EvaluatorBuilder", "expandRule", "Field and type: " + field + ", " + type);
        
        if (type.isEmpty()) type = "string";            // Default to string as results will all be strings
        
        boolean isString = type.equals("string");
        boolean isDate = type.equals("date");
        boolean isTime = type.equals("time");
        boolean isDateTime = type.equals("datetime");
        
        String prefix = "";
        if (isDate)
            prefix = DATE_PREFIX; 
        
        switch (((String) thisNode.get(OPERATOR)).toLowerCase())
        {
            case BETWEEN:
                if (isString) throw new ExpressionException(BETWEEN + " is valid only for numbers/dates");
                
                if (isDate || isTime || isDateTime)
                {
                    @SuppressWarnings("unchecked") ArrayList<String> betnVals =  (ArrayList<String>) thisNode.get(VALUE);
                    expression += SPACE + prefix + DATE_GE + SPACE + betnVals.get(0) + SPACE + AND + SPACE + thisNode.get(FIELD).toString() + SPACE + prefix + DATE_LE + SPACE + betnVals.get(1) + CLOSE_PAREN;
                }
                else
                {
                    @SuppressWarnings("unchecked") ArrayList<Number> betnVals =  (ArrayList<Number>) thisNode.get(VALUE);
                    expression += SPACE + GE + SPACE + betnVals.get(0) + SPACE + AND + SPACE + thisNode.get(FIELD).toString() + SPACE + LE + SPACE + betnVals.get(1) + CLOSE_PAREN;
                }
                break;

            case NOT_BETWEEN:
                if (isString) throw new ExpressionException(NOT_BETWEEN + " is valid only for numbers/dates");
                
                if (isDate || isTime || isDateTime)
                {
                    @SuppressWarnings("unchecked") ArrayList<String> notBetnVals =  (ArrayList<String>) thisNode.get(VALUE);
                    expression += SPACE + prefix + DATE_LE + SPACE + notBetnVals.get(0) + SPACE + AND + SPACE + thisNode.get(FIELD).toString() + SPACE + prefix + DATE_GE + SPACE + notBetnVals.get(1) + CLOSE_PAREN;
                }
                else
                {          
                    @SuppressWarnings("unchecked") ArrayList<Number> notBetnVals =  (ArrayList<Number>) thisNode.get(VALUE);
                    expression += SPACE + LE + notBetnVals.get(0) + SPACE + AND + SPACE + thisNode.get(FIELD).toString() + SPACE + GE + SPACE + notBetnVals.get(1) + CLOSE_PAREN;
                }
                break;
                
            case GREATER:
                if (isString) throw new ExpressionException(GREATER + " is valid only for numbers/dates");
                if (isDate || isTime || isDateTime)
                    expression += SPACE + prefix + DATE_GT + SPACE;
                else
                    expression += SPACE + GT + SPACE;
                break;

            case GREATER_OR_EQUAL:
                if (isString) throw new ExpressionException(GREATER_OR_EQUAL + " is valid only for numbers/dates");

                if (isDate || isTime || isDateTime)
                    expression += SPACE + prefix + DATE_GE + SPACE;
                else
                    expression += SPACE + GE + SPACE;
                break;

            case LESSER:
                if (isString) throw new ExpressionException(LESSER + " is valid only for numbers/dates");
                
                if (isDate || isTime || isDateTime)
                    expression += SPACE + prefix + DATE_LT + SPACE;
                else
                    expression += SPACE + LT + SPACE;
                break;
                
            case LESSER_OR_EQUAL:
                if (isString) throw new ExpressionException(LESSER_OR_EQUAL + " is valid only for numbers/dates");
                
                if (isDate || isTime || isDateTime)
                    expression += SPACE + prefix + DATE_LE + SPACE; 
                else
                    expression += SPACE + LE + SPACE;
                break;
                
            case EQUALS:
                if (isString || isDate || isTime || isDateTime)
                    expression += SPACE + prefix + EQUALS + SPACE;
                else
                    expression += SPACE + EQ + SPACE;
                break;
                
            case NOT_EQUALS:
                if (isString || isDate || isTime || isDateTime)
                    expression += SPACE + prefix + NOT + EQUALS + SPACE;
                else
                    expression += SPACE + NE + SPACE;
                break;
                
            case IN:
                if (!isString) throw new ExpressionException(IN + " is valid only for strings");
                expression += SPACE + IN + SPACE;
                break;

            case NOT_IN:
                if (!isString) throw new ExpressionException(NOT_IN + " is valid only for strings");
                expression += SPACE + NOT + IN + SPACE;
                break;

            case BEGINS_WITH:
                if (!isString) throw new ExpressionException(BEGINS_WITH + " is valid only for strings");
                expression += SPACE + BEGINS + SPACE;
                break;

            case DOES_NOT_BEGIN_WITH:
                if (!isString) throw new ExpressionException(DOES_NOT_BEGIN_WITH + " is valid only for strings");
                expression += SPACE + NOT + BEGINS + SPACE;;
                break;

            case ENDS_WITH:
                if (!isString) throw new ExpressionException(ENDS_WITH + " is valid only for strings");
                expression += SPACE + ENDS + SPACE;
                break;

            case DOES_NOT_END_WITH:
                if (!isString) throw new ExpressionException(DOES_NOT_END_WITH + " is valid only for strings");
                expression += SPACE + NOT + ENDS + SPACE;
                break;

            case CONTAINS:
                if (!isString) throw new ExpressionException(CONTAINS + " is valid only for strings");
                expression += SPACE + CONTAINS + SPACE;
                break;

            case DOES_NOT_CONTAIN:
                if (!isString) throw new ExpressionException(DOES_NOT_CONTAIN + " is valid only for strings");
                expression += SPACE + NOT + CONTAINS + SPACE;
                break;
                
            case IS_EMPTY:
                if (!isString && !isDate && !isTime && !isDateTime) throw new ExpressionException(IS_EMPTY + " is valid only for strings and dates");
                expression += SPACE + EMPTY + SPACE;
                break;

            case IS_NOT_EMPTY:
                if (!isString && !isDate && !isTime && !isDateTime) throw new ExpressionException(IS_NOT_EMPTY + " is valid only for strings and dates");
                expression += SPACE + NOT + EMPTY + SPACE;
                break;

            case IS_EMPTY_LIST:
                if (!isString) throw new ExpressionException(IS_EMPTY_LIST + " is valid only for strings");
                expression += SPACE + EMPTY_LIST + SPACE;
                break;

            case IS_NOT_EMPTY_LIST:
                if (!isString) throw new ExpressionException(IS_NOT_EMPTY_LIST + " is valid only for strings");
                expression += SPACE + NOT + EMPTY_LIST + SPACE;
                break;

            case IS_NULL:
                expression += SPACE + NULL + SPACE;
                break;

            case IS_NOT_NULL:
                expression += SPACE + NOT + NULL + SPACE;
                break;

            default:
                throw new ExpressionException("Unknown Operator: " + thisNode.get(OPERATOR));
        }
        
        String operator = thisNode.get(OPERATOR).toString().toLowerCase();
        logger.addDebugLog("EvaluatorBuilder", "expandRule", "Operator: " + operator);
        if (!operator.equals(BETWEEN) && !operator.equals(NOT_BETWEEN))
        {
            if (!operator.equals(IS_EMPTY) && !operator.equals(IS_NOT_EMPTY)
                    && !operator.equals(IS_EMPTY_LIST) && !operator.equals(IS_NOT_EMPTY_LIST) 
                    && !operator.equals(IS_NULL) && !operator.equals(IS_NOT_NULL)) {
                expression += isString ? DBL_QUOTE : "";
                if (thisNode.get(VALUE) == null)
                    throw new ExpressionException("Fatal error: Field: " + field + ", Operator: " + operator + ", value not specified for expression");
                    
                String val = thisNode.get(VALUE).toString();
                if (isString) {
                    if (val.startsWith("\"")) val = val.substring(1);
                    if (val.endsWith("\"")) val = val.substring(0, val.length()-1);
                }
                expression += val;
                expression += isString ? DBL_QUOTE : "";
            }
            expression += CLOSE_PAREN;
        }
        
        return expression;
    }
    
    // Checks if the given node is a rule or a condition/rules
    private boolean aRule(JsonNode node) throws Exception
    {
        boolean isRule = true;
        ObjectMapper mapper = new ObjectMapper();
        
        Map<String, JsonNode> thisNode =  mapper.readValue(node.toString(), new TypeReference<LinkedHashMap<String, JsonNode>>() {});
        if (thisNode.containsKey(CONDITION) && thisNode.containsKey(RULES))
        {
            isRule = false;
        }
        return isRule;
    }

    // Removes all variables that are indicated as ${var} and changes them to just var
    private String removeVariablePlaceHolder(String expression)
    {
        String regex = "\\$\\{[a-zA-Z\\-_0-9]*\\}";
        
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(expression);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) 
        {
            // Is this a calculate result remove the leading stepname and _
            // A different step result will be processed via priorResults and will have the _result in it
            String replacement = expression.substring(matcher.start()+2, matcher.end()-1);
            if (replacement.contains("_") && !replacement.startsWith("env_") && !replacement.endsWith("_result")) {
                replacement = replacement.substring(replacement.indexOf("_")+1);
            }
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        };            
        matcher.appendTail(sb);
        return sb.toString();
    }
}
