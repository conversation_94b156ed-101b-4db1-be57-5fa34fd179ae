from flask import Flask, request, jsonify, render_template, Response
from flask_cors import CORS
import requests
import os
import base64
import logging
import traceback
import time
import json
from requests.exceptions import SSLError, ConnectionError, Timeout, RequestException

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(filename)s - Line: %(lineno)d - %(message)s')
logger = logging.getLogger(__name__)

app = Flask("Workflow Uploader")
CORS(app)

class WorkflowUploader:
    def __init__(self, url, company_id, login_id, password, disable_ssl_verification=False, upload_delay=1.0):
        self.url = url
        if (not self.url.endswith('/')):
            self.url += '/'
        if (not self.url.endswith('/UMP/')):
            self.url += 'UMP/'

        self.company_id = company_id
        self.login_id = login_id
        self.password = password

        # SSL verification setting
        # For localhost/127.0.0.1 or when explicitly disabled, skip SSL verification
        self.verify_ssl = not (disable_ssl_verification or 'localhost' in self.url or '127.0.0.1' in self.url)

        # Upload delay to prevent overwhelming the server
        self.upload_delay = upload_delay

        self.appPath = "API/v3/applications/DIGITAL_FORMS"
        self.apiPath = "API/v3/workflow/"

        self.error = ""
        self.output = ""
        self.upload_count = 0
        self.failed_uploads = []

        logger.info(f"Uploading to: {self.url} for company: {self.company_id} (SSL verification: {self.verify_ssl}, Upload delay: {self.upload_delay}s)")

    def authenticate(self):
        try:
            response = requests.get(f"{self.url}{self.appPath}", auth=(self.company_id + '\\' + self.login_id, self.password), timeout=10, verify=self.verify_ssl)
            if response.status_code != 200:
                # Try to parse JSON response, but handle cases where it's not JSON (e.g., HTML error pages)
                try:
                    self.error = response.json()['error']
                except (ValueError, KeyError):
                    # If response is not JSON or doesn't have 'error' key, use status and text
                    self.error = f"HTTP {response.status_code}: {response.text[:200]}"
                logger.error(f"Error authenticating: {self.error}")
                return False

            logger.info(f"Authenticated successfully")
            return True
        except SSLError as e:
            self.error = f"SSL Certificate Error: {str(e)}. Consider using a trusted certificate or adding the certificate to your trust store."
            logger.error(f"SSL Error during authentication: {self.error}")
            return False
        except ConnectionError as e:
            self.error = f"Connection Error: {str(e)}"
            logger.error(f"Connection Error during authentication: {self.error}")
            return False
        except Timeout as e:
            self.error = f"Request Timeout: {str(e)}"
            logger.error(f"Timeout during authentication: {self.error}")
            return False
        except RequestException as e:
            self.error = f"Request Error: {str(e)}"
            logger.error(f"Request Error during authentication: {self.error}")
            return False
    
    def read_from_file(self, filename):
        file_contents = ""
        description = ""
        
        logger.info(f"Reading from file: {filename}")
        with open(filename, "r", encoding="utf-8") as file:
            for line in file:
                if "@description:" in line:
                    description = line.split("@description:", 1)[1].strip()
                file_contents += line
        # Encode the full content in Base64
        encoded_content = base64.b64encode(file_contents.encode("utf-8")).decode("utf-8")
        
        logger.info(f"Found description and contents: {description}")
        return encoded_content, description    
    
    def upload_operation_or_flow(self, wf_name, wf_type, max_retries=2):
        current_dir = os.path.dirname(__file__)
        filename = os.path.join(current_dir, 'Content', 'unvired', wf_type, wf_name)
        if not wf_name.endswith('.sl'):
            filename += '.sl'

        if not os.path.exists(filename):
            self.error += f"Error uploading {wf_name}, file not found<br>"
            self.failed_uploads.append(f"{wf_name} (file not found)")
            logger.error(f"Error file not found: {wf_name}")
            return False

        # Read from the file
        encoded_content, description = self.read_from_file(filename)

        data = {
            "wfType": f"{wf_type}",
            "wfName": f"{wf_name}",
            "wfSubType": "system",
            "wfNamespace": f"unvired.{wf_type}",
            "wfTitle": f"{wf_name.upper()}",
            "wfDescription": f"{description}",
            "wfContent": f"{encoded_content}"
        }

        # Retry logic for handling temporary server issues
        for attempt in range(max_retries + 1):
            if attempt > 0:
                wait_time = attempt * 2  # Exponential backoff: 2s, 4s, 6s...
                logger.info(f"Retrying {wf_name} (attempt {attempt + 1}/{max_retries + 1}) after {wait_time}s delay...")
                time.sleep(wait_time)

            logger.info(f"Uploading workflow: {wf_name} (attempt {attempt + 1})")
            try:
                response = requests.patch(self.url + self.apiPath + wf_name, auth=(self.company_id + '\\' + self.login_id, self.password), timeout=15, verify=self.verify_ssl, data=data)

                if response.status_code == 204 or response.status_code == 201:
                    self.upload_count += 1
                    self.output += f"✅ {wf_name} uploaded (attempt {attempt + 1})<br>"
                    logger.info(f"Uploaded successfully {wf_name}")
                    return True
                elif response.status_code == 502:  # Bad Gateway
                    error_msg = f"Bad Gateway (502) for {wf_name}"
                    if attempt < max_retries:
                        logger.warning(f"{error_msg} - will retry")
                        continue  # Retry this upload
                    else:
                        self.error += f"❌ {error_msg} - max retries exceeded<br>"
                        self.failed_uploads.append(f"{wf_name} (Bad Gateway)")
                        logger.error(f"Max retries exceeded for {wf_name}")
                        return False
                else:
                    # Other HTTP errors
                    try:
                        error_detail = response.json()['error']
                    except (ValueError, KeyError):
                        error_detail = f"HTTP {response.status_code}: {response.text[:200]}"

                    self.error += f"❌ Error uploading {wf_name}: {error_detail}<br>"
                    self.failed_uploads.append(f"{wf_name} ({response.status_code})")
                    logger.error(f"Error uploading {wf_name}: {error_detail}")
                    return False

            except (SSLError, ConnectionError, Timeout, RequestException) as e:
                error_msg = f"{type(e).__name__} for {wf_name}: {str(e)}"
                if attempt < max_retries and isinstance(e, (ConnectionError, Timeout)):
                    logger.warning(f"{error_msg} - will retry")
                    continue  # Retry for connection/timeout errors
                else:
                    self.error += f"❌ {error_msg}<br>"
                    self.failed_uploads.append(f"{wf_name} ({type(e).__name__})")
                    logger.error(f"Error uploading {wf_name}: {error_msg}")
                    return False

        return False

    def upload_standard_workflow(self):
        standard_operations = ["ads", "asyncsubflow", "calculate", "csvparser", "jsonparser", "query", "storedproc",
						"filestore", "flowresult", "formapprovaldecision", "formresultinternal", "ftp",
						"response", "odata", "execjava", "printtext",
						"http", "condition", "execjavascript", "execpython", "sap", "email", "merge"]
        standard_flows = ["contact", "formalert", "formarchive", "formassign", "approvalworkflow",
	                    "formcreate", "formpdf", "formread", "formsearch", "formshare",
						"formupdate", "masterdatareload", "masterdataread", "masterdataupdate"]

        total_files = len(standard_operations) + len(standard_flows)
        logger.info(f"---> Uploading {total_files} standard operations and flows with {self.upload_delay}s delay between uploads")

        try:
            # Upload operations
            for i, opn in enumerate(standard_operations, 1):
                self.output += f"📤 Uploading operation {i}/{len(standard_operations)}: {opn}<br>"
                success = self.upload_operation_or_flow(opn, 'operation')

                if not success:
                    # Continue with other files even if one fails
                    logger.warning(f"Failed to upload operation {opn}, continuing with remaining files")

                # Add delay between uploads to prevent overwhelming the server
                if i < len(standard_operations):  # Don't delay after the last operation
                    time.sleep(self.upload_delay)

            # Upload flows
            for i, flow in enumerate(standard_flows, 1):
                self.output += f"📤 Uploading flow {i}/{len(standard_flows)}: {flow}<br>"
                success = self.upload_operation_or_flow(flow, 'flow')

                if not success:
                    # Continue with other files even if one fails
                    logger.warning(f"Failed to upload flow {flow}, continuing with remaining files")

                # Add delay between uploads to prevent overwhelming the server
                if i < len(standard_flows):  # Don't delay after the last flow
                    time.sleep(self.upload_delay)

            # Summary
            success_count = self.upload_count
            failed_count = len(self.failed_uploads)
            self.output += f"<br>📊 <strong>Upload Summary:</strong><br>"
            self.output += f"✅ Successful: {success_count}/{total_files}<br>"
            if failed_count > 0:
                self.output += f"❌ Failed: {failed_count}/{total_files}<br>"
                self.output += f"Failed files: {', '.join(self.failed_uploads)}<br>"

            logger.info(f"---> Completed uploading standard workflows. Success: {success_count}, Failed: {failed_count}")

        except Exception as e:
            traceback.print_exc()
            logger.error(e)
            self.error += f"System error : {str(e)}<br>"
            logger.error(f"Error uploading: {self.error}")

        # Return success if at least some files uploaded successfully
        if self.upload_count > 0:
            status = "success" if len(self.failed_uploads) == 0 else "partial_success"
            return {"status": status, "message": f"{self.output}"}
        else:
            return {"status": "error", "message": f"{self.error}"}

    def upload_standard_workflow_streaming(self):
        """Generator function that yields progress updates for each file upload"""
        standard_operations = ["ads", "asyncsubflow", "calculate", "csvparser", "jsonparser", "query", "storedproc",
						"filestore", "flowresult", "formapprovaldecision", "formresultinternal", "ftp",
						"response", "odata", "execjava", "printtext",
						"http", "condition", "execjavascript", "execpython", "sap", "email", "merge"]
        standard_flows = ["contact", "formalert", "formarchive", "formassign", "approvalworkflow",
	                    "formcreate", "formpdf", "formread", "formsearch", "formshare",
						"formupdate", "masterdatareload", "masterdataread", "masterdataupdate"]

        total_files = len(standard_operations) + len(standard_flows)
        current_file = 0

        yield {
            "type": "start",
            "message": f"Starting upload of {total_files} files...",
            "total": total_files,
            "current": 0,
            "status": "info"
        }

        try:
            # Upload operations
            for i, opn in enumerate(standard_operations, 1):
                current_file += 1
                yield {
                    "type": "progress",
                    "message": f"Uploading operation {i}/{len(standard_operations)}: {opn}",
                    "total": total_files,
                    "current": current_file,
                    "status": "uploading",
                    "file_name": opn,
                    "file_type": "operation"
                }

                success = self.upload_operation_or_flow(opn, 'operation')

                if success:
                    yield {
                        "type": "file_complete",
                        "message": f"✅ {opn} uploaded successfully",
                        "total": total_files,
                        "current": current_file,
                        "status": "success",
                        "file_name": opn,
                        "file_type": "operation"
                    }
                else:
                    yield {
                        "type": "file_complete",
                        "message": f"❌ {opn} failed to upload",
                        "total": total_files,
                        "current": current_file,
                        "status": "error",
                        "file_name": opn,
                        "file_type": "operation"
                    }

                # Add delay between uploads
                if i < len(standard_operations):
                    time.sleep(self.upload_delay)

            # Upload flows
            for i, flow in enumerate(standard_flows, 1):
                current_file += 1
                yield {
                    "type": "progress",
                    "message": f"Uploading flow {i}/{len(standard_flows)}: {flow}",
                    "total": total_files,
                    "current": current_file,
                    "status": "uploading",
                    "file_name": flow,
                    "file_type": "flow"
                }

                success = self.upload_operation_or_flow(flow, 'flow')

                if success:
                    yield {
                        "type": "file_complete",
                        "message": f"✅ {flow} uploaded successfully",
                        "total": total_files,
                        "current": current_file,
                        "status": "success",
                        "file_name": flow,
                        "file_type": "flow"
                    }
                else:
                    yield {
                        "type": "file_complete",
                        "message": f"❌ {flow} failed to upload",
                        "total": total_files,
                        "current": current_file,
                        "status": "error",
                        "file_name": flow,
                        "file_type": "flow"
                    }

                # Add delay between uploads
                if i < len(standard_flows):
                    time.sleep(self.upload_delay)

            # Final summary
            success_count = self.upload_count
            failed_count = len(self.failed_uploads)

            yield {
                "type": "complete",
                "message": f"Upload complete! Success: {success_count}/{total_files}, Failed: {failed_count}/{total_files}",
                "total": total_files,
                "current": total_files,
                "status": "complete",
                "success_count": success_count,
                "failed_count": failed_count,
                "failed_files": self.failed_uploads
            }

        except Exception as e:
            yield {
                "type": "error",
                "message": f"System error: {str(e)}",
                "total": total_files,
                "current": current_file,
                "status": "error"
            }

    def upload_custom_workflow(self, custom_type, wf_name):
        # Custom workflow upload implementation based on type and text
        try:
            # Remove extension if user has enetered it
            wf_name_no_ext = wf_name
            if wf_name_no_ext.endswith('.sl'):
                wf_name_no_ext = wf_name_no_ext[:-3]

            logger.info(f"---> Uploading custom {custom_type} : {wf_name}")
            self.upload_operation_or_flow(wf_name_no_ext, custom_type.lower())
            logger.info(f"---> Completed uploading custom {custom_type} : {wf_name}")            

        except Exception as e:
            traceback.print_exc()
            logger.error(e)
            self.error += f"System error : {str(e)}"
            logger.error(f"Error uploading: {self.error}")

        if self.error:
            return {"status": "error", "message": f"{self.error}"}
        
        return {"status": "success", "message": f"{self.output}"}            

@app.route('/')
def index():
    logger.info(f"Returning upload page")
    return render_template('index.html')

@app.route('/hc')
def health_check():
    logger.info(f"Healthcheck")
    return jsonify({"status": "healthy"}), 200
    
@app.route('/upload', methods=['POST'])
def upload_workflow():

    logger.info(f"Received upload request")

    data = request.json
    # Check if SSL verification should be disabled (optional parameter)
    disable_ssl = data.get('disableSSLVerification', False)
    # Check upload delay setting (default 1.5 seconds for standard uploads)
    upload_delay = data.get('uploadDelay', 1.5 if data.get('uploadType') == 'standard' else 0.5)

    uploader = WorkflowUploader(
        data['url'],
        data['companyId'],
        data['loginId'],
        data['password'],
        disable_ssl_verification=disable_ssl,
        upload_delay=upload_delay
    )

    # Authenticate and confirm
    if not uploader.authenticate():
        return {"status": "error", "message": f"{uploader.error}"}

    if data['uploadType'] == 'standard':
        result = uploader.upload_standard_workflow()
    else:
        result = uploader.upload_custom_workflow(
            data['customType'], 
            data['wfName']
        )

    return jsonify(result)

@app.route('/upload-stream', methods=['POST'])
def upload_workflow_stream():
    """Streaming upload endpoint that provides real-time progress updates"""
    logger.info(f"Received streaming upload request")

    data = request.json
    # Check if SSL verification should be disabled (optional parameter)
    disable_ssl = data.get('disableSSLVerification', False)
    # Check upload delay setting (default 1.5 seconds for standard uploads)
    upload_delay = data.get('uploadDelay', 1.5 if data.get('uploadType') == 'standard' else 0.5)

    uploader = WorkflowUploader(
        data['url'],
        data['companyId'],
        data['loginId'],
        data['password'],
        disable_ssl_verification=disable_ssl,
        upload_delay=upload_delay
    )

    # Authenticate first
    if not uploader.authenticate():
        return jsonify({"status": "error", "message": f"{uploader.error}"})

    def generate():
        """Generator function for Server-Sent Events"""
        if data['uploadType'] == 'standard':
            for update in uploader.upload_standard_workflow_streaming():
                yield f"data: {json.dumps(update)}\n\n"
        else:
            # For custom uploads, use the regular method but wrap in streaming format
            result = uploader.upload_custom_workflow(
                data['customType'],
                data['wfName']
            )
            yield f"data: {json.dumps({'type': 'complete', 'result': result})}\n\n"

    return Response(generate(), mimetype='text/plain')

if __name__ == '__main__':
   app.run(port=8000)