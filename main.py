from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import requests
import os
import base64
import logging
import traceback

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(filename)s - Line: %(lineno)d - %(message)s')
logger = logging.getLogger(__name__)

app = Flask("Workflow Uploader")
CORS(app)

class WorkflowUploader:
    def __init__(self, url, company_id, login_id, password):
        self.url = url
        if (not self.url.endswith('/')):
            self.url += '/'
        if (not self.url.endswith('/UMP/')):
            self.url += 'UMP/'

        self.company_id = company_id            
        self.login_id = login_id
        self.password = password

        self.appPath = "API/v3/applications/DIGITAL_FORMS"
        self.apiPath = "API/v3/workflow/"
        
        self.error = ""
        self.output = ""

        logger.info(f"Uploading to: {self.url} for company: {self.company_id}")

    def authenticate(self):
        response = requests.get(f"{self.url}{self.appPath}", auth=(self.company_id + '\\' + self.login_id, self.password), timeout=10, verify=not ('localhost' in self.url or '127.0.0.1' in self.url))
        if response.status_code != 200:
            self.error = response.json()['error']
            logger.error(f"Error authenticating: {self.error}")
            return False
        
        logger.info(f"Authenticated successfully")
        return True
    
    def read_from_file(self, filename):
        file_contents = ""
        description = ""
        
        logger.info(f"Reading from file: {filename}")
        with open(filename, "r", encoding="utf-8") as file:
            for line in file:
                if "@description:" in line:
                    description = line.split("@description:", 1)[1].strip()
                file_contents += line
        # Encode the full content in Base64
        encoded_content = base64.b64encode(file_contents.encode("utf-8")).decode("utf-8")
        
        logger.info(f"Found description and contents: {description}")
        return encoded_content, description    
    
    def upload_operation_or_flow(self, wf_name, wf_type):
        current_dir = os.path.dirname(__file__)
        filename = os.path.join(current_dir, 'Content', 'unvired', wf_type, wf_name)
        if not wf_name.endswith('.sl'):
            filename += '.sl'

        if not os.path.exists(filename):
            self.error += f"Error uploading {wf_name}, file not found"
            logger.error(f"Error file not found: {self.error}")
            return False            

        # Read from the file
        encoded_content, description = self.read_from_file(filename)            

        data = {
            "wfType": f"{wf_type}",
            "wfName": f"{wf_name}",
            "wfSubType": "system",
            "wfNamespace": f"unvired.{wf_type}",
            "wfTitle": f"{wf_name.upper()}",
            "wfDescription": f"{description}",
            "wfContent": f"{encoded_content}"
        }

        logger.info(f"Uploading workflow: {wf_name}")
        response = requests.patch(self.url + self.apiPath + wf_name, auth=(self.company_id + '\\' + self.login_id, self.password), timeout=10, verify=not ('localhost' in self.url or '127.0.0.1' in self.url), data=data)
        if response.status_code == 204 or response.status_code == 201:
            self.output += f"{wf_name} uploaded<br>"
            logger.info(f"Uploaded successfully {wf_name}")
        else:
            self.error += f"Error uploading {wf_name}<br>"
            self.error += response.json()['error']
            logger.error(f"Error uploading: {self.error}")
            return False

        return True

    def upload_standard_workflow(self):
        standard_operations = ["ads", "asyncsubflow", "calculate", "csvparser", "jsonparser", "query", "storedproc",
						"filestore", "flowresult", "formapprovaldecision", "formresultinternal", "ftp",
						"response", "odata", "execjava", "printtext", 
						"http", "condition", "execjavascript", "execpython", "sap", "email", "merge"]
        standard_flows = ["contact", "formalert", "formarchive", "formassign", "approvalworkflow",
	                    "formcreate", "formpdf", "formread", "formsearch", "formshare", 
						"formupdate", "masterdatareload", "masterdataread", "masterdataupdate"]
        
        logger.info(f"---> Uploading all standard operations and flows")
        try:
            for opn in standard_operations:
                if not self.upload_operation_or_flow(opn, 'operation'):
                    break

            for flow in standard_flows:
                if not self.upload_operation_or_flow(flow, 'flow'):
                    break

            logger.info(f"---> Completed uploading all standard operations and flows")

        except Exception as e:
            traceback.print_exc()
            logger.error(e)
            self.error += f"System error : {str(e)}"
            logger.error(f"Error uploading: {self.error}")

        if self.error:
            return {"status": "error", "message": f"{self.error}"}
        
        return {"status": "success", "message": f"{self.output}"}        

    def upload_custom_workflow(self, custom_type, wf_name):
        # Custom workflow upload implementation based on type and text
        try:
            # Remove extension if user has enetered it
            wf_name_no_ext = wf_name
            if wf_name_no_ext.endswith('.sl'):
                wf_name_no_ext = wf_name_no_ext[:-3]

            logger.info(f"---> Uploading custom {custom_type} : {wf_name}")
            self.upload_operation_or_flow(wf_name_no_ext, custom_type.lower())
            logger.info(f"---> Completed uploading custom {custom_type} : {wf_name}")            

        except Exception as e:
            traceback.print_exc()
            logger.error(e)
            self.error += f"System error : {str(e)}"
            logger.error(f"Error uploading: {self.error}")

        if self.error:
            return {"status": "error", "message": f"{self.error}"}
        
        return {"status": "success", "message": f"{self.output}"}            

@app.route('/')
def index():
    logger.info(f"Returning upload page")
    return render_template('index.html')

@app.route('/hc')
def health_check():
    logger.info(f"Healthcheck")
    return jsonify({"status": "healthy"}), 200
    
@app.route('/upload', methods=['POST'])
def upload_workflow():

    logger.info(f"Received upload request")

    data = request.json
    uploader = WorkflowUploader(
        data['url'], 
        data['companyId'],
        data['loginId'], 
        data['password']
    )

    # Authenticate and confirm
    if not uploader.authenticate():
        return {"status": "error", "message": f"{uploader.error}"}

    if data['uploadType'] == 'standard':
        result = uploader.upload_standard_workflow()
    else:
        result = uploader.upload_custom_workflow(
            data['customType'], 
            data['wfName']
        )
        
    return jsonify(result)

if __name__ == '__main__':
   app.run(port=8000)