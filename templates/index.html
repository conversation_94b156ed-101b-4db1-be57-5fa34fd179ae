<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow Uploader</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/alpinejs/3.12.0/cdn.min.js" defer></script>
    <style>
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .main-heading {
            margin-bottom: 20px;
            font-size: 2em;
        }
        .container {
            background-color: #1e1e1e;
            border-radius: 15px;
            padding: 30px;
            width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        input, select {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            background-color: #2c2c2c;
            border: 1px solid #444;
            color: #e0e0e0;
            border-radius: 5px;
        }
        .radio-group {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .radio-group label {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .custom-options {
            background-color: #2c2c2c;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .custom-options h3 {
            margin-top: 0;
            margin-bottom: 15px;
            text-align: left;
        }
        button {
            width: 100%;
            padding: 10px;
            background-color: #4a4a4a;
            color: #e0e0e0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #5a5a5a;
        }
        #output {
            margin-top: 15px;
            padding: 10px;
            background-color: #2c2c2c;
            border-radius: 5px;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
            color: #888;
            white-space: pre-wrap;
            text-align: left;
        }
        .icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <h1 class="main-heading">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" class="icon" viewBox="0 0 16 16">
            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
        </svg>
        Workflow Uploader
    </h1>
    <div class="container" x-data="workflowUploader()">
        <div class="form-group">
            <label>URL *</label>
            <input type="text" x-model="url" required placeholder="UMP Host URL">
        </div>
        <div class="form-group">
            <label>Company ID *</label>
            <input type="text" x-model="companyId" required placeholder="Company">
        </div>
        <div class="form-group">
            <label>Login ID *</label>
            <input type="text" x-model="loginId" required placeholder="Login Id or Email">
        </div>
        <div class="form-group">
            <label>Password *</label>
            <input type="password" x-model="password" required placeholder="Password">
        </div>
        <div class="form-group radio-group">
            <label>
                <input type="radio" x-model="uploadType" value="standard">
                Standard
            </label>
            <label>
                <input type="radio" x-model="uploadType" value="custom">
                Custom
            </label>
        </div>
        <template x-if="uploadType === 'custom'">
            <div class="custom-options">
                <h3>Options</h3>
                <div class="form-group radio-group">
                    <label>
                        <input type="radio" x-model="customType" value="operation">
                        Operation
                    </label>
                    <label>
                        <input type="radio" x-model="customType" value="flow">
                        Flow
                    </label>
                </div>
                <div class="form-group">
                    <input type="text" x-model="wfName" required placeholder="Workflow <filename.sl>" >
                </div>
            </div>
        </template>
        <button @click="uploadWorkflow">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="icon" viewBox="0 0 16 16">
                <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
            </svg>
            Upload
        </button>
        <div id="output" x-html="output || 'Awaiting upload...'"></div>
        <div id="loadingOverlay" x-show="isLoading" style="
            position: absolute; 
            top: 0; 
            left: 0; 
            right: 0; 
            bottom: 0; 
            background-color: rgba(0, 0, 0, 0.5); 
            z-index: 1000; 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            margin: 0; 
            padding: 0;">
        </div>        
    </div>

    <script>
        function workflowUploader() {
            return {
                url: 'https://live.unvired.io/',
                isLoading: false,
                companyId: 'UNVIRED',
                loginId: '',
                password: '',
                uploadType: 'standard',
                customType: 'operation',
                wfName: '',
                output: '',
                async uploadWorkflow() {
                    // Check standard fields
                    if (!this.url || !this.loginId || !this.companyId || !this.password) {
                        this.output = 'Please fill in all required fields.';
                        return false;
                    }

                    // Additional validation for custom type
                    if (this.uploadType === 'custom' && (!this.wfName || !this.wfName.trim())) {
                        this.output = 'Workflow Name is required for custom upload.';
                        return false;
                    }

                    this.output = 'Uploading...';
                    document.body.style.cursor = 'wait';
                    this.isLoading = true;
                    try {
                        const response = await fetch('/upload', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                url: this.url.trim(),
                                companyId: this.companyId.trim().toUpperCase(),
                                loginId: this.loginId.trim(),
                                password: this.password.trim(),
                                uploadType: this.uploadType,
                                customType: this.customType,
                                wfName: this.wfName.trim()
                            })
                        });
                        const result = await response.json();
                        this.output = result.message;
                        return true;
                    } catch (error) {
                        this.output = `Error: ${error.message}`;
                        return false;
                    }
                    finally {
                        document.body.style.cursor = 'default';
                        this.isLoading = false;
                    }
                }
            };
        }
    </script>
</body>
</html>
