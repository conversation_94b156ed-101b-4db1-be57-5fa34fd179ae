#-- ##### System generated do not modify #####
def execute(inputdata, loglevel):

	#-- ##### System generated inputs do not modify #####
	#-- ##### loglevel can be either info or debug to help write out console.log statements
	import json
	inputobj = json.loads(inputdata)
	someinput = inputobj["someinput"]

	#-- ##### The result of this snippet is a dictionary of variable names and values #####
	#-- ##### Select all the variables that you need to pass on to other workflow steps #####
	#-- ##### and return dictionary of variable names and values to propagate in the workflow #####
	#-- ##### Assignments such as result['error'] will be extracted and returned to the workflow #####
	#-- ##### Make one assignment per line to ensure results are extracted correctly #####
	result = {}

	#-- The error if any
	error = ""

	#-- ##### Make your changes after this line #####
	# This is an example, you can remove these lines safely
	if loglevel == "debug":
		print "Some log statement"

	somevar = "something to return"

	result['error'] = error
	result['somevar'] = somevar

	return result

#-- ##### (Optional) private functions to call from execute() can be added here #####

#-- ##### (Optional) private functions end #####

#-- ##### System generated do not modify #####
if __name__ == '__main__':
	execute(inputdata, loglevel)