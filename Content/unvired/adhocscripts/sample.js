//-- ##### System generated do not modify #####
function execute(inputdata, loglevel)
{
	//-- ##### System generated inputs do not modify #####
	//-- ##### loglevel can be either info or debug to help write out console.log statements
	inputobj = JSON.parse(inputdata);
	someinput = inputobj["someinput"];

	//-- ##### The result of this snippet is a stringified JSON object of variable names and values #####
	//-- ##### Select all the variables that you need to pass on to other workflow steps #####
	//-- ##### and return JSON.stringify() of variable names and values to propagate in the workflow #####
	//-- ##### Assignments such as result['error'] will be extracted and returned to the workflow #####
	//-- ##### Make one assignment per line to ensure results are extracted correctly #####
	result = {};

	//-- The error if any
	error = "";

	//-- ##### Make your changes after this line #####
	// This is an example, you can remove these lines safely
	if (loglevel == "debug")
		console.log("Print some log statement");

	somevar = "something to return";

	result['error'] = error;
	result['somevar'] = somevar;

	return JSON.stringify(result);
}

//-- ##### (Optional) private functions to call from execute() can be added here #####

//-- ##### (Optional) private functions end #####
