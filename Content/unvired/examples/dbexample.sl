#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Run a SQL query on a database or insert/update/delete data directly using JSON input
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Database system name to use.
#!               $type text
#!               $example forms-db
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input query: The query to execute on the database.
#!               $type text
#!               $example select * from customer;
#!               $notes The query can be any valid Select, Insert, Update or Delete SQL query.  If multi-query support is enabled, multiple queries separated by ; can also be executed
#!               $notes Either query or operation has to be specified for a valid execution
#!               $required Optional
#! @input operation: Instead of specifying the SQL statement, an operation can be specified here and JSON data passed in to update in the database.  
#!                   $type list
#!                   $listvalues: insert, update, delete
#!                   $example insert
#!                   $notes In this example the statement to be executed is insert  and the specified data will be inserted into the table
#!                   $notes Either query or operation has to be specified for a valid execution
#!                   $required Optional
#! @input keys: The keys of the table(s) for the specified operation  
#!              $type text
#!              $example {"keys":{"salesOrder":"orderID", "orderItem":["pkID","fkIDOrder"]}}
#!              $notes In this example the operation will be performed with salesOrder table having orderID as primary key,
#!              $notes orderItem table having pkID as the primary key and fkIDOrder as the foreign key
#!              $notes Required parameter if operation is specified instead of query
#!              $required Optional
#! @input data: The data to be persisted in the database as JSON for the specified operation
#!              $type text
#!              $example {"salesOrder":{"orderId":12,"customer":"CPB1000100","deliveryDate":"2020-01-28"},"orderItem":[{"fkIDOrder":12,"pkID":"10","matNumber":"CPB11600","quantity":10,"unit":"CSE"},{"fkIDOrder":12,"pkID":"20","matNumber":"CPB11100","quantity":2,"unit":"L"}]}
#!              $notes The above input will result in data for salesOrder table and columns orderId, customer and deliveryDate and two rows        
#!              $notes of data for orderItem table with fkIDOrder the foreign key, pkID the primary key and columns matNumber
#!              $notes quantity and unit (of measure)
#!              $notes Required parameter if operation is specified instead of query
#!              $required Optional
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: dbexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - query:
        required: false
    - operation:
        required: false        
    - keys:
        required: false        
    - data:
        required: false

  workflow:
    - readrow:
        do:
          ops.dbrequest:
            - alias
            - namespace
            - application
            - convId
            - system
            - query
            - operation
            - keys
            - data
        publish:
          - result
          - error
        navigate:
          - SUCCESS: prevStepName_debugbreak
          - FAILURE: prevStepName_debugbreak

    - prevStepName_debugbreak:
        do:
          ops.debugbreak:
            - alias
            - namespace
            - application
            - convId
            - prevstepname: "prevstepname"
            - breakpoints: "breakpoints"
            - prevresult: ${result}
            - preverror: ${error}
        publish:
          -  result
          -  error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${result}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE