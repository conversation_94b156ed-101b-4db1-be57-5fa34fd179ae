#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any REST request on a remote server
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The REST system endpoint definition
#!               $type text
#!               $example dropbox
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input contenttype: The content type
#!               $type list
#!               $listvalues text/plain, application/json, application/xml, application/x-www-form-urlencoded, multipart/form-data
#!               $example application/json
#!               $notes The content type for the HTTP call
#!               $required Mandatory
#! @input accept: The accept type
#!               $type list
#!               $listvalues application/json, application/xml
#!               $example application/json
#!               $notes The accept type for the HTTP call
#!               $required Mandatory
#! @input httpverb: The HTTP verb (action) to call
#!               $type list
#!               $listvalues get, post, put, patch, delete
#!               $example get
#!               $notes The HTTP verb or action to call
#!               $required Mandatory
#! @input postquerystring: Indicates if the post parameters also have to be sent as a query string
#!               $type list
#!               $listvalues true, false
#!               $example false
#!               $notes The default value is false if this parameter is not specified
#!               $required Optional
#! @input instanceurl: With some services like Salesforce, ServiceNow etc, the URL is dynamic and may need to be changed from what is configured in the system properties
#!               This is usually referred to as the instanceUrl and can be set to override the system properties
#!               $type text
#!               $example https://na14.salesforce.com
#!               $notes The default value is the URL configured in the system properties if this parameter is not specified
#!               $required Optional
#! @input urlpath: The (url) path for the call
#!               This is usually referred to as the instanceUrl and can be set to override the system properties
#!               $type text
#!               $example /login
#!               $notes The urlPath is appended to the configured URL or to the instanceURL specified
#!               $required Optional
#! @input attachments: Comma separated list of Attachment IDs of the files to be posted to the end point.  The attachment IDs are obtained by uploading the files to the Unvired Platform using the attachmentrequest workflow
#!               $type text
#!               $example ATT123456, ATT789012
#!               $notes The attachments already need to be uploaded before posting to external service
#!               $required Optional
#! @input headerparameters: Comma separated list of header parameters to pass to this call 
#!               $type text
#!               $example "auth-token" will add one header parameter auth-token to the final call
#!               $notes If headerparameters is set to an empty string, no header parameter will be included.
#!               $notes To include all header parameters posted to the executeWorkflow REST API (except system defined ones), do not set headerparameters
#!               $required Optional
#! @input postparameters: Comma separated list of post parameters to pass to this call 
#!               $type text
#!               $example  "firstname, lastname" will add two post parameters firstname and lastname to the final call
#!               $notes If postparameters is set to an empty string, no post parameter will be included.
#!               $notes To include all post parameters posted to the executeWorkflow REST API (excluding system defined ones), do not set postparameters
#!               $required Optional
#! @input body: Data to be posted as raw body to the endpoint 
#!               $type text
#!               $example {"key": "some value that needs to be posted to the webhook in the body of the call:}
#!               $notes The specified content is written to the body of the request call
#!               $required Optional
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: restexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - formdata
    - contenttype: application/json
    - accept: application/json
    - httpverb: POST
    - postquerystring:
        required: false
    - instanceurl:
        required: false
    - urlpath:
        required: false
    - attachments:
        required: false
    - headerparameters:
        required: false
    - postparameters:
        required: false        
    - body:
        required: false  

  workflow:
    - callhttp:
        do:
          ops.restrequest:
            - alias
            - namespace
            - application
            - convId
            - system
            - contenttype
            - accept: ${get_field("accept", "")}
            - httpverb
            - postquerystring: "true"
            - instanceurl
            - urlpath
            - attachments
            - headerparameters
            - postparameters
            - body
        publish:
          - result
          - error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${result}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE