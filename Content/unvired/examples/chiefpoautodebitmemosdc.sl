#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: 
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formdata: The submitted form data
#!               $type text
#!               $example Form data JSON representation
#!               $required Optional
#!               $notes System managed input, automatically filled
#! @input eventdata: The data from any event raised (primarily) for SDC
#!               $type text
#!               $example Event data as published by custom event
#!               $required Optional
#!               $notes This is valid only for the Smart Data Component or SDC
#!               $notes and is System managed input, automatically filled
#! @input formid: Unique ID identifying this Workflow execution
#!               $type system
#!               $example Unique Indentifier 
#!               $required Optional
#!               $notes System managed input, automatically filled
#! @input formfields: JSON array of all the fields in this form
#!               $type system
#!               $example Form fields JSON representation 
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#! 
#! @output result: The result of the function execution
#! @output error: The error from the function execution if any
#! 
#! @result SUCCESS: If the function executed successfully
#! @result FAILURE: If there was an error while executing the function
#! 
#!!#
############################################################################################################################################

namespace: unvired.flow
imports:
  ops: unvired.operation
flow:
  name: chiefpoautodebitmemosdc
  inputs:
   - alias
   - namespace
   - application
   - convId
   - formdata:
      default: ""
      required: false
   - eventdata:
      default: ""
      required: false
   - formId:
      default: ""
      required: false
   - submissionId:
      default: ""
      required: false
  workflow:
   - saveandreturndebitmemo:
      do:
        ops.dbsprequest:
         - alias
         - namespace
         - application
         - convId
         - system: "cidebitmemosample"
         - spname: "Save_Debit_Memo_Header"
#         - spinput: ${"{\"PoNo\":{\"type\":\"in\",\"value\":\"" + get_field("po", "243") + "\"}}"}
         - spinput: ${"{\"PoNo\":{\"type\":\"in\",\"value\":\"243\"}}"}
         - formdata: "${formdata}"
         - eventdata: "${eventdata}"
         - formId: "${formId}"
         - submissionId: "${submissionId}"
      publish:
       - result
       - error
       - saveandreturndebitmemo_result: ${result}
      navigate:
       - SUCCESS: flow_result
       - FAILURE: flow_error
   - flow_result:
      do:
        ops.flowresult:
         - alias
         - namespace
         - application
         - convId
         - outputdata: ${result}
         - outputerror: ""
      publish:
       - finalResult
       - finalError
      navigate:
       - SUCCESS: SUCCESS
       - FAILURE: FAILURE
   - flow_error:
      do:
        ops.flowresult:
         - alias
         - namespace
         - application
         - convId
         - outputdata: ""
         - outputerror: ${error}
      publish:
       - finalResult
       - finalError
      navigate:
       - SUCCESS: SUCCESS
       - FAILURE: FAILURE
  results:
   - SUCCESS
   - FAILURE