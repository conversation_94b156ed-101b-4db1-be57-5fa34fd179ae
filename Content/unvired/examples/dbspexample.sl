#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Run a SQL stored procedure on a database
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Database system name to use.
#!               $type text
#!               $example forms-db
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input spname: The name of the stored procedure (SP) to execute
#!               $type text
#!               $example spCountRowsConditional
#!               $notes This will execute the spCountRowsConditional stored procedure
#!               $required Mandatory
#! @input spinput: The input for the stored procedure (SP) specified above. A valid input provides the input and output parameters as JSON.
#!               $type text
#!               $example {"whereclause":{"type":"in","value":"2=2"}, "countrows":{"type":"OUT"}}
#!               $notes This input specifies two parameters to the stored procedure. The whereclause is an in parameter to the SP and the 
#!               $notes countrows is an out parameter.  Once the SP executes with the specified whereclause, the countrows will be the 
#!               $notes result returned as defined in the optional fieldMap
#!               $required Optional
#! @input outputfieldmap: The additional mapping for the SP specified above.  A valid outputfieldmap will map database fields to output
#!                  fields as required in the output JSON
#!                  $type text
#!                  $example {"countrows":"count"}
#!                  $notes This maps the SP returned column countrows to count in the returned JSON data resulting in a result: {"count": 20}
#!                  $required Optional
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: dbspexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - spname
    - spinput:
        required: false
    - outputfieldmap:
        required: false

  workflow:
    - readrow:
        do:
          ops.dbsprequest:
            - alias
            - namespace
            - application
            - convId
            - system
            - spname
            - spinput
            - outputfieldmap
        publish:
          - result
          - error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${result}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE