#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any Unvired Application Function (or Process Agent Java Function)
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input function: The logical name of the function to execute as configured in the Unvired Application
#!               $type text
#!               $example PA_DIGITAL_FORMS_GET_FORMS
#!               $required Mandatory
#!               $notes These are Java functions that have been created using the Unvired Modeller and use the Unvired SDK
#!               $notes to interface with SAP S4/HANA, Salesforce, Databases and any REST/SOAP backend systems.
#! @input fninput: The additional input for the function specified above
#!               $type json
#!               $example {"customer":"unvired"}
#!               $notes A valid input is a well formed XML/JSON as defined in the Unvired Modeller.  If this input is not specified, ALL Post parameters (except system defined ones)
#!               $notes which are posted to the executeWorkflow REST API will be passed to the function as input
#!               $required Optional
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: getformfield

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata: "{\"srini\":\"1\",\"first\":2,\"second\":3}"

  workflow:
    - readfield:
        do:
          ops.printtext:
            - text: ${get_field("srini1", "none")} 
        publish:
          - result1: ${get_field("srini", "none")} 
        navigate:
          - SUCCESS: SUCCESS

  results:
    - SUCCESS
