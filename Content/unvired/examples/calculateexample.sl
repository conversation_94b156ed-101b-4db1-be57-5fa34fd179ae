#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any Unvired Application Function (or Process Agent Java Function)
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in workflow or as required
#!               $type text
#!               $required Mandatory
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!              $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other workflow steps
#! @input expressions: JSON representation of the variables and the expressions to calculate.  A maximum of 50 expressions can be processed in a batch
#!               $type text
#!               $example {"variable":"expression"}
#!               $notes A valid input is a well formed XML/JSON as defined in the Unvired Modeller.  If this input is not specified, ALL Post parameters (except system defined ones)
#!               $required Mandatory
#!
#! @output result: The result of the expressions calculation as JSON string
#! @output error: The error from the expressions calculation if any
#!
#! @result SUCCESS: If the expressions were calculated successfully
#! @result FAILURE: If there was an error while calculating the expressions
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: calculateexample

  inputs:
    - alias
    - namespace
    - application
    - convId

  workflow:
    - docalc:
        do:
          ops.calculaterequest:
            - alias
            - namespace
            - application
            - convId
            - formdata: "{\"firstName\":\"John\", \"lastName\":\"Doe\", \"bank\" : 10000, \"bonds\": 15000, \"stocks\": 35000}"
            - expressions: "{\"name\":\"concat(firstName,\\\" \\\",lastName)\",\"debt\":\"bank+bonds\",\"networth\":\"debt+stocks\"}"
        publish:
          - name: ${systemresult.split("_~_")[0]}
          - debt: ${systemresult.split("_~_")[1]}
          - networth: ${systemresult.split("_~_")[2]}
          - result
          - error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_error

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${name + " has a net worth of $" + networth}
            - outputerror: ""
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

    - flow_error:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ""
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE