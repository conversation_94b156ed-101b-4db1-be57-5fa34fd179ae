#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Data mapper example to test
#!
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: pyexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - inputdata
    - scriptname: "sample"

  workflow:
    - calljs:
        do:
          ops.runpyscript:
            - alias
            - namespace
            - application
            - convId
            - inputdata
            - scriptname
            - contentpath: "/Users/<USER>/SourceCode/UMP4/UMP_HOME/content/adhocscripts/"
        publish:
          - somevar: ${get_str(eval(result)['somevar']) if ('somevar' in eval(result)) else str('')}
          - onesomevar: ${get_str(eval(result)['onesomevar']) if ('onesomevar' in eval(result)) else str('')}
          - error: ${get_str(eval(result)['error']) if ('error' in eval(result)) else str('')}
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${somevar}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE