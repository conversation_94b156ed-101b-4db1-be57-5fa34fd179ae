#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Run a SQL query on a database or insert/update/delete data directly using JSON input
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Database system name to use.
#!               $type text
#!               $example forms-db
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input query: The query to execute on the database.
#!               $type text
#!               $example select * from customer;
#!               $notes The query can be any valid Select, Insert, Update or Delete SQL query.  If multi-query support is enabled, multiple queries separated by ; can also be executed
#!               $notes Either query or operation has to be specified for a valid execution
#!               $required Optional
#! @input operation: Instead of specifying the SQL statement, an operation can be specified here and JSON data passed in to update in the database.  
#!                   $type list
#!                   $listvalues: insert, update, delete
#!                   $example insert
#!                   $notes In this example the statement to be executed is insert  and the specified data will be inserted into the table
#!                   $notes Either query or operation has to be specified for a valid execution
#!                   $required Optional
#! @input keys: The keys of the table(s) for the specified operation  
#!              $type text
#!              $example {"keys":{"salesOrder":"orderID", "orderItem":["pkID","fkIDOrder"]}}
#!              $notes In this example the operation will be performed with salesOrder table having orderID as primary key,
#!              $notes orderItem table having pkID as the primary key and fkIDOrder as the foreign key
#!              $notes Required parameter if operation is specified instead of query
#!              $required Optional
#! @input data: The data to be persisted in the database as JSON for the specified operation
#!              $type text
#!              $example {"salesOrder":{"orderId":12,"customer":"CPB1000100","deliveryDate":"2020-01-28"},"orderItem":[{"fkIDOrder":12,"pkID":"10","matNumber":"CPB11600","quantity":10,"unit":"CSE"},{"fkIDOrder":12,"pkID":"20","matNumber":"CPB11100","quantity":2,"unit":"L"}]}
#!              $notes The above input will result in data for salesOrder table and columns orderId, customer and deliveryDate and two rows        
#!              $notes of data for orderItem table with fkIDOrder the foreign key, pkID the primary key and columns matNumber
#!              $notes quantity and unit (of measure)
#!              $notes Required parameter if operation is specified instead of query
#!              $required Optional
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: sapexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system:
        default: "sapides"
        required: false
    - function:
        default: "MEREP_CONTACT_GETDETAIL"
        required: false
    - parameters:
        default: "{\"RFCInput\":[\"PERSNUMBER\",\"PERSON\",\"RETURN\",\"ADDRESS\",\"E_MAIL\"],\"RFCOutput\":[\"PERSON\",\"RETURN\",\"ADDRESS\",\"E_MAIL\"],\"RFCParameter\":[{\"NAME\":\"PERSNUMBER\",\"DESC\":\"ME Replica: Personal number (Sample application)\",\"INDEX\":\"0\",\"TYPE\":\"6\",\"LENGTH\":\"0\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"0\"},{\"NAME\":\"PERSON\",\"DESC\":\"ME Relica: Person (Sample Application)\",\"INDEX\":\"1\",\"TYPE\":\"17\",\"LENGTH\":\"0\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"MEREP_PERSON\",\"DIRECTION\":\"1\",\"RFCField\":[{\"NAME\":\"MANDT\",\"DESC\":\"Client\",\"INDEX\":\"0\",\"TYPE\":\"0\",\"LENGTH\":\"3\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"PERSNUMBER\",\"DESC\":\"Person Number (Sample Application)\",\"INDEX\":\"1\",\"TYPE\":\"6\",\"LENGTH\":\"10\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"1\"},{\"NAME\":\"FIRST_NAME\",\"DESC\":\"First Name (Sample Application)\",\"INDEX\":\"2\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"LAST_NAME\",\"DESC\":\"Last Name (Sample Application)\",\"INDEX\":\"3\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"PROFESSION\",\"DESC\":\"Profession (Sample Application)\",\"INDEX\":\"4\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"SEX\",\"DESC\":\"Gender (Sample Application)\",\"INDEX\":\"5\",\"TYPE\":\"0\",\"LENGTH\":\"1\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"BIRTHDAY\",\"DESC\":\"Birthday (Sample Application)\",\"INDEX\":\"6\",\"TYPE\":\"1\",\"LENGTH\":\"8\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"DATE\",\"DIRECTION\":\"1\"},{\"NAME\":\"WEIGHT\",\"DESC\":\"Weight (Sample Application)\",\"INDEX\":\"7\",\"TYPE\":\"2\",\"LENGTH\":\"3\",\"DECIMAL\":\"2\",\"SAP_TYPE\":\"BCD\",\"DIRECTION\":\"1\"},{\"NAME\":\"HEIGHT\",\"DESC\":\"Height (Sample Application)\",\"INDEX\":\"8\",\"TYPE\":\"2\",\"LENGTH\":\"3\",\"DECIMAL\":\"2\",\"SAP_TYPE\":\"BCD\",\"DIRECTION\":\"1\"},{\"NAME\":\"CATEGORY1\",\"DESC\":\"Category1 (Sample Application)\",\"INDEX\":\"9\",\"TYPE\":\"6\",\"LENGTH\":\"2\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"1\"},{\"NAME\":\"CATEGORY2\",\"DESC\":\"Category2 (Sample Application)\",\"INDEX\":\"10\",\"TYPE\":\"0\",\"LENGTH\":\"20\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"CREDAT\",\"DESC\":\"Created on\",\"INDEX\":\"11\",\"TYPE\":\"1\",\"LENGTH\":\"8\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"DATE\",\"DIRECTION\":\"1\"},{\"NAME\":\"CRENAM\",\"DESC\":\"Created by\",\"INDEX\":\"12\",\"TYPE\":\"0\",\"LENGTH\":\"12\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"CRETIM\",\"DESC\":\"Create Time\",\"INDEX\":\"13\",\"TYPE\":\"3\",\"LENGTH\":\"6\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"TIME\",\"DIRECTION\":\"1\"},{\"NAME\":\"CHGDAT\",\"DESC\":\"Last Changed on\",\"INDEX\":\"14\",\"TYPE\":\"1\",\"LENGTH\":\"8\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"DATE\",\"DIRECTION\":\"1\"},{\"NAME\":\"CHGNAM\",\"DESC\":\"Last Changed by\",\"INDEX\":\"15\",\"TYPE\":\"0\",\"LENGTH\":\"12\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"CHGTIM\",\"DESC\":\"Last Changed at\",\"INDEX\":\"16\",\"TYPE\":\"3\",\"LENGTH\":\"6\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"TIME\",\"DIRECTION\":\"1\"}]},{\"NAME\":\"RETURN\",\"DESC\":\"Return Parameter\",\"INDEX\":\"2\",\"TYPE\":\"17\",\"LENGTH\":\"0\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"BAPIRET2\",\"DIRECTION\":\"1\",\"RFCField\":[{\"NAME\":\"TYPE\",\"DESC\":\"Message type: S Success, E Error, W Warning, I Info, A Abort\",\"INDEX\":\"0\",\"TYPE\":\"0\",\"LENGTH\":\"1\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"ID\",\"DESC\":\"Message Class\",\"INDEX\":\"1\",\"TYPE\":\"0\",\"LENGTH\":\"20\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"NUMBER\",\"DESC\":\"Message Number\",\"INDEX\":\"2\",\"TYPE\":\"6\",\"LENGTH\":\"3\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"1\"},{\"NAME\":\"MESSAGE\",\"DESC\":\"Message Text\",\"INDEX\":\"3\",\"TYPE\":\"0\",\"LENGTH\":\"220\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"LOG_NO\",\"DESC\":\"Application log: log number\",\"INDEX\":\"4\",\"TYPE\":\"0\",\"LENGTH\":\"20\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"LOG_MSG_NO\",\"DESC\":\"Application log: Internal message serial number\",\"INDEX\":\"5\",\"TYPE\":\"6\",\"LENGTH\":\"6\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"1\"},{\"NAME\":\"MESSAGE_V1\",\"DESC\":\"Message Variable\",\"INDEX\":\"6\",\"TYPE\":\"0\",\"LENGTH\":\"50\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"MESSAGE_V2\",\"DESC\":\"Message Variable\",\"INDEX\":\"7\",\"TYPE\":\"0\",\"LENGTH\":\"50\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"MESSAGE_V3\",\"DESC\":\"Message Variable\",\"INDEX\":\"8\",\"TYPE\":\"0\",\"LENGTH\":\"50\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"MESSAGE_V4\",\"DESC\":\"Message Variable\",\"INDEX\":\"9\",\"TYPE\":\"0\",\"LENGTH\":\"50\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"PARAMETER\",\"DESC\":\"Parameter Name\",\"INDEX\":\"10\",\"TYPE\":\"0\",\"LENGTH\":\"32\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"ROW\",\"DESC\":\"Lines in parameter\",\"INDEX\":\"11\",\"TYPE\":\"8\",\"LENGTH\":\"4\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"INT\",\"DIRECTION\":\"1\"},{\"NAME\":\"FIELD\",\"DESC\":\"Field in parameter\",\"INDEX\":\"12\",\"TYPE\":\"0\",\"LENGTH\":\"30\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"},{\"NAME\":\"SYSTEM\",\"DESC\":\"Logical system from which message originates\",\"INDEX\":\"13\",\"TYPE\":\"0\",\"LENGTH\":\"10\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"1\"}]},{\"NAME\":\"ADDRESS\",\"DESC\":\"ME Relica: Address (Sample Application)\",\"INDEX\":\"3\",\"TYPE\":\"99\",\"LENGTH\":\"0\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"MEREP_ADDRESS\",\"DIRECTION\":\"3\",\"RFCField\":[{\"NAME\":\"MANDT\",\"DESC\":\"Client\",\"INDEX\":\"0\",\"TYPE\":\"0\",\"LENGTH\":\"3\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"PERSNUMBER\",\"DESC\":\"Person Number (Sample Application)\",\"INDEX\":\"1\",\"TYPE\":\"6\",\"LENGTH\":\"10\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"3\"},{\"NAME\":\"SEQNO_ADR\",\"DESC\":\"Seqeunce Number (Sample Application)\",\"INDEX\":\"2\",\"TYPE\":\"6\",\"LENGTH\":\"10\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"3\"},{\"NAME\":\"CITY1\",\"DESC\":\"City1 (Sample Application)\",\"INDEX\":\"3\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"CITY2\",\"DESC\":\"City2 (Sample Application)\",\"INDEX\":\"4\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"STREET\",\"DESC\":\"Street (Sample Application)\",\"INDEX\":\"5\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"HOUSE_NUM\",\"DESC\":\"House Number (Sample Application)\",\"INDEX\":\"6\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"REGION\",\"DESC\":\"Region (Sample Application)\",\"INDEX\":\"7\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"LAND\",\"DESC\":\"Country (Sample Application)\",\"INDEX\":\"8\",\"TYPE\":\"0\",\"LENGTH\":\"3\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"}]},{\"NAME\":\"E_MAIL\",\"DESC\":\"ME Relica: E-mail addressa (Sample Application)\",\"INDEX\":\"4\",\"TYPE\":\"99\",\"LENGTH\":\"0\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"MEREP_E_MAIL\",\"DIRECTION\":\"3\",\"RFCField\":[{\"NAME\":\"MANDT\",\"DESC\":\"Client\",\"INDEX\":\"0\",\"TYPE\":\"0\",\"LENGTH\":\"3\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"PERSNUMBER\",\"DESC\":\"Person Number (Sample Application)\",\"INDEX\":\"1\",\"TYPE\":\"6\",\"LENGTH\":\"10\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"3\"},{\"NAME\":\"SEQNO_E_MAIL\",\"DESC\":\"Seqeunce Number (Sample Application)\",\"INDEX\":\"2\",\"TYPE\":\"6\",\"LENGTH\":\"10\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"NUM\",\"DIRECTION\":\"3\"},{\"NAME\":\"E_ADDR\",\"DESC\":\"E-mail Address (Sample Application)\",\"INDEX\":\"3\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"},{\"NAME\":\"E_ADDR_TEXT\",\"DESC\":\"E-mail Address Description (Sample Application)\",\"INDEX\":\"4\",\"TYPE\":\"0\",\"LENGTH\":\"40\",\"DECIMAL\":\"0\",\"SAP_TYPE\":\"CHAR\",\"DIRECTION\":\"3\"}]}]}"
        required: false
    - data:
        default: "{\"PERSNUMBER\":\"1000\"}"
        required: false

  workflow:
    - readrow:
        do:
          ops.saprequest:
            - alias
            - namespace
            - application
            - convId
            - system
            - function
            - parameters    
            - data
        publish:
          - result
          - error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${result}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE