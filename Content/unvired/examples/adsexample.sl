#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Eaxmple flow to query a Microsoft Active Directory Service for user and other information
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Active Directory (ADS) system name to use.
#!               $type text
#!               $example ads-server
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input action: The action to perform on the Active Directory server.
#!               $type list
#!               $listvalues   Authenticate,  GetAttributes, SetAttribute,   ListObjects,  SearchObjects
#!               $example Authenticate
#!               $notes  Authenticate: Authenticate the give domain, user and password on the ADS server
#!               $notes GetAttributes: Retrieves the givev User's attributes from the ADS
#!               $notes SetAttribute: Sets the provided attrubute for the user. Attributes may be added (Add), changed (Replace) or deleted (Delete) as specified in the input
#!               $notes ListObjects: Retrieves a list of users matching the listing (input) criteria
#!               $notes SearchObjects: Searches for a list of users matching the searc (input) criteria
#!               $required Mandatory
#! @input operation: The operation to perform, required if action is SetAttributes  
#!               $type list
#!               $listvalues Add, Replace, Delete
#!               $example Replace
#!               $notes  attributeOperation - Adds, modifies or deletes the specified attribute with the provided value
#!               $required Optional
#! @input searchcontrols: The scope of the search to be performed, required if action is SearchObjects  
#!               $type list
#!               $listvalues Object, OneLevel, SubTree
#!               $example Object
#!               $notes  Determines the scope of the ADS search, name=field name 
#!               $required Optional
#! @input name: The attibute to modify or the field to search for  
#!               $type text
#!               $example commonName
#!               $notes  Required if the specified Action is SetAttribute or SearchObjects  
#!               $required Optional
#! @input value: The value to set for the attibute or the field value to search for  
#!               $type text
#!               $example unvired
#!               $notes  Required if the specified Action is SetAttribute or SearchObjects  
#!               $required Optional
#! @input basedn: base DN or point from where to search for users  
#!               $type text
#!               $example com.unvired/Engineering
#!               $notes  Can be specified in the System Properties file, set this if the value has to be changed for the specific call  
#!               $required Optional
#! @input domain: ADS domain of the authenticating user  
#!               $type text
#!               $example unvired.com
#!               $notes  Can be specified in the System Properties file, set this if the value has to be changed for the specific call  
#!               $required Optional
#! @input user: ADS user id of the authenticating user  
#!               $type text
#!               $example john
#!               $notes  Can be specified in the System Properties file for search etc. For authenticating a user, set this to the username  
#!               $required Optional
#! @input password: ADS password of the authenticating user  
#!               $type text
#!               $example xxxxxxxx
#!               $notes  Can be specified in the System Properties file for search etc. For authenticating a user, set this to the password
#!               $required Optional
#!
#! @output result: The result of the ADS workflow specified
#! @output error: The error from the ADS workflow if any
#!
#! @result SUCCESS: If the workflow executed successfully
#! @result FAILURE: If there was an error while executing the workflow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: adsexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - action
    - operation:
        required: false
    - searchcontrols:
        required: false    
    - name:
        required: false    
    - value:
        required: false    
    - basedn:
        required: false    
    - domain:
        required: false    
    - user:
        required: false    
    - password:
        required: false
        sensitive: true

  workflow:
    - queryads:
        do:
          ops.adsrequest:
            - alias
            - namespace
            - application
            - convId
            - system
            - action
            - operation
            - searchcontrols
            - name
            - value
            - basedn
            - domain
            - user
            - password
        publish:
          - result
          - error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${result}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE