#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Data mapper example to test
#!
#!
#! @output result: The result of the flow
#! @output error: The error from the flow if any
#!
#! @result SUCCESS: If the flow completed successfully
#! @result FAILURE: If there was an error while executing the flow
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: mapperexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - sourcedata
    - mapexpression:  "{\"debitMemoData\":debitMemoData.{\"rmano\":rmano,\"debitMemo\":debitMemo,\"notetext\":notetext,
            \"items\":serviceHistory.[{\"buyUnitPrice\":buyUnitPrice, \"qtyReturned\":qtyReturned}
            ]}}"
#{ \"CustomerID\": data.customer.id,\"ContactName\":data.customer.contactName,\"CompanyName\":data.customer.companyName,\"Country\":data.customer.country}
  workflow:
    - callmapper:
        do:
          ops.datamapper:
            - alias
            - namespace
            - application
            - convId
            - sourcedata
            - mapexpression
        publish:
          - result
          - error
        navigate:
          - SUCCESS: flow_result
          - FAILURE: flow_result

    - flow_result:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ${result}
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE