#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any Unvired Application Function (or Process Agent Java Function)
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formdata: The input form data to calculate from.
#!               $type json
#!               $required Mandatory
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!              $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other workflow steps
#! @input rules: JSON representation of the rules to evaulate. The rules are configured using the rules UI
#!               $type json
#!               $notes A valid input is a JSON as defined in the rules UI.
#!               $required Mandatory
#!
#! @output result: The result of the rules evaluated as JSON string
#! @output error: The error from the expressions calculation if any
#!
#! @result THEN: If the rules were evaluated to be True
#! @result ELSE: If the rules were evaluated to be False
#! @result FAILURE: If there was an error while evaluating the rules
#!!#
############################################################################################################################################
namespace: unvired.flow

imports:
  ops: unvired.operation

flow:
  name: rulesexample

  inputs:
    - alias
    - namespace
    - application
    - convId
    - submissionId

  workflow:
    - do_calc:
        do:
          ops.calculaterequest:
            - alias
            - namespace
            - application
            - convId
            - submissionId
            - formdata: "{\"poNo\":\"Test12\",\"submit\":true,\"customer\":\"CPB1000110\",\"dataGrid\":[{\"uom\":\"L\",\"itemNo\":10,\"material\":\"CPB11600\",\"quantity\":12,\"matDescription\":\"FOREIGN BEER 18 L KEG RETURNABLE\"}],\"orderType\":\"OR\",\"salesArea\":\"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division\",\"deliveryDate\":\"2020-05-23T00:00:00+05:30\",\"paymentTerms\":\"0002\"}"
            - expressions: "{\"name\":\"concat(poNo,\\\" \\\",customer)\"}"
        publish:
          - name: ${systemresult.split("_~_")[0]}
          - result
          - error
        navigate:
          - SUCCESS: do_rules
          - FAILURE: flow_error

    - do_rules:
        do:
          ops.rulesrequest:
            - alias
            - namespace
            - application
            - convId
            - submissionId
            - formdata: "{\"poNo\":\"Test12\",\"submit\":true,\"customer\":\"CPB1000110\",\"dataGrid\":[{\"uom\":\"L\",\"itemNo\":10,\"material\":\"CPB11600\",\"quantity\":12,\"matDescription\":\"FOREIGN BEER 18 L KEG RETURNABLE\"}],\"orderType\":\"OR\",\"salesArea\":\"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division\",\"deliveryDate\":\"2020-05-23T00:00:00+05:30\",\"paymentTerms\":\"0002\"}"
            - rules: "{\"condition\":\"or\",\"rules\":[{\"field\":\"orderType\",\"operator\":\"equal\",\"value\":\"ORS\"},{\"field\":\"customer\",\"operator\":\"equal\",\"value\":\"CPB1000110\"}]}"
        publish:
          - result
          - error
        navigate:
          - THEN: stocks_more_than_cash
          - ELSE: cash_more_than_stocks
          - FAILURE: flow_error

    - stocks_more_than_cash:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: "THEN CONDITION"
            - outputerror: ""
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

    - cash_more_than_stocks:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: "ELSE CONDITION"
            - outputerror: ""
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

    - flow_error:
        do:
          ops.flowresult:
            - alias
            - namespace
            - application
            - convId
            - outputdata: ""
            - outputerror: ${error}
        publish:
          -  finalResult
          -  finalError
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  results:
    - SUCCESS
    - FAILURE