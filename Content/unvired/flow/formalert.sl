#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Send an alert to a user or team
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["comment"]},{"missing_some":[1,["users","teams"]]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formusers: The users collaborating on the form, will be set automatically
#!               $type system
#!               $required optional
#! @input comment: The alert message / comment
#!               $type textarea
#!               $label Alert Message
#!               $example Related to WO# 21212 from Service desk
#!               $required Mandatory
#! @input recipienttype: The type of recipients
#!               $label Recipients
#!               $type radio
#!               $listvalues Users, Teams
#!               $default Users
#!               $example Users
#!               $notes Recipients can be Users or Teams
#!               $required Mandatory
#!               $generation false
#! @input users:  User (named user who is part of the company/domain) to whom the alert needs to be sent to.
#!               $type user
#!               $label Recipients
#!               $example <EMAIL>
#!               $multiple true
#!               $defaultSelect users
#!               $required Optional
#!               $notes Either one of users or teams needs to be provided
#! @input teams: Teams to whom the alert needs to be sent to.
#!               $type team
#!               $label Recipient Teams
#!               $example rigteam
#!               $multiple true
#!               $defaultSelect teams
#!               $required Optional
#!               $notes Either one of users or teams needs to be provided
#! @input severity: The alert severity
#!               $type list
#!               $label Alert Severity
#!               $listvalues Error, Warning, Info, Mention
#!               $default Info
#!               $example Info
#!               $required Optional
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Submission Data
#!               $required optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#! @input taskId: Unique ID identifying the task, provide this if the alert is related to this task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes This is typically the taskId available in the current flow (set to $taskId} for the form being processed
#!               $notes or the result of a create form flow call
#! @input failonerror: Fail the flow on error
#!               $type boolean
#!               $label Fail flow On Error
#!               $default true
#!               $example true
#!               $notes If this is a non-critical step in the flow, failonerror can be set to false so that the flow will continue
#!               $notes to execute the next step
#!               $required Optional
#!
#! @output result: Alert text on success
#! @output error: The error from the alert if any (for e.g. user or team not found)
#!
#! @result SUCCESS: If the send was successful
#! @result FAILURE: If there was an error while sending the alert
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formalert

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata:
        default: "{}"
        required: false
    - formusers:
        default: "{}"
        required: false
    - taskId:
        default: ""
        required: false
    - users:
        default: ""
        required: false
    - teams:
        default: ""
        required: false
    - comment
    - severity:
        default: "info"
        required: false
    - failonerror:
        default: 'true'
        required: false

  workflow:
    - form_alert:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - failonerror
            - function: "DIGITAL_FORMS_PA_WF_FORM_ALERT"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"comment\":\"" + comment + "\",\"formdata\":" + enc_json_str(formdata) + ",\"formusers\":" + enc_json_str(formusers) + ",\"users\":\"" + users + "\",\"teams\":\"" + teams + "\",\"severity\":\"" + severity + "\",\"failonerror\":\"" + failonerror + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE