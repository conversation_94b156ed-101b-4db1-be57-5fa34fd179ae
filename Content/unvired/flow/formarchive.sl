#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Archive the form (cannot be modified any longer) and create a link to to view the archived version
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input submissionId: Unique ID identifying the form submission (required if the task is for a formset)
#!               $type system
#!               $default ${submissionId}
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes This is typically the taskId available in the current flow (set to $taskId} for the form being processed
#!               $notes or the result of a create form flow call
#!
#! @output result: Archive link on success
#! @output error: The error from the archive link creation if any (for e.g. form submission not found or not completed)
#!
#! @result SUCCESS: If the archiving was successful
#! @result FAILURE: If there was an error while creating the archive
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formarchive

  inputs:
    - alias
    - namespace
    - application
    - convId
    - taskId:
        required: false
    - submissionId:
        required: false

  workflow:
    - form_archive:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_ARCHIVE"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"submissionId\":\"" + submissionId + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE