#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Manage contacts (external users)
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["action","email"]},{"missing_some":[2,{"if":[{"==":[{"var":"action"},"Create"]},["firstname","lastname"],[]]}]},{"missing_some":[1,{"if":[{"==":[{"var":"action"},"Update"]},["firstname","lastname","phone"],[]]}]},{"missing_some":[1,{"if":[{"==":[{"var":"action"},"Delete"]},["email"],[]]}]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input action: The action to perform with the external user.  Update will overwrite the existing external user or create if not found
#!               $type radio
#!               $label Action
#!               $listvalues Update, Delete
#!               $default Update
#!               $example Update
#!               $required Mandatory
#! @input email: Email ID of the external user
#!               $type email
#!               $label Email ID
#!               $example <EMAIL>
#!               $required Mandatory
#!               $notes External users are uniquely identified by their email id.
#! @input firstname: First name of the user
#!               $type text
#!               $label First Name
#!               $example John
#!               $required Optional
#!               $notes First Name needs to be provided for Create and optionally for update
#! @input lastname: Last name of the user
#!               $type text
#!               $label Last Name
#!               $example Doe
#!               $required Optional
#!               $notes Lasst Name needs to be provided for Create and optionally for update
#! @input phone: Phone number of the external user
#!               $type text
#!               $label Phone
#!               $example ****** 111 1111
#!               $required Optional
#! @input private: Is the contact private to the user creating it?
#!               $type boolean
#!               $label Private Contact
#!               $example False
#!               $default False
#!               $required Optional
#!               $notes Public contacts will be shared with other users within your organization
#!
#! @output result: Unique ID of the created/updated/deleted user
#! @output error: The error from the external user creation if any
#!
#! @result SUCCESS: If the external user creation / modification / deletion was successful
#! @result FAILURE: If there was an error while performing the required action on the external user
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: contact

  inputs:
    - alias
    - namespace
    - application
    - convId
    - action
    - firstname:
        default: ""
        required: false
    - lastname:
        default: ""
        required: false
    - email
    - private:
        default: "false"
        required: false
    - phone:
        default: ""
        required: false

  workflow:
    - contact_create:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_CREATE_EXTERNAL_USER"
            - fninput: ${"{\"action\":\"" + action + "\",\"firstname\":\"" + firstname + "\",\"lastname\":\"" + lastname + "\",\"email\":\"" + email + "\",\"private\":\"" + private + "\",\"phone\":\"" + phone + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE