#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Read Master Data as part of the flow
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formId: Unique ID identifying this master data form
#!               $type system
#!               $example xxxxxxxxx
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input masterdataname: The Master data entity to read
#!               $type masterdataname
#!               $label Master Data Entity
#!               $multiple: false
#!               $defaultSelect forms
#!               $example equipment
#!               $required Mandatory
#! @input filter: The filter to apply on the data
#!               $type text
#!               $label Master Data Filter
#!               $required Optional
#!
#! @output result: Array of masterdata JSON objects
#! @output error: The error if any while reading the masterdata
#!
#! @result SUCCESS: If the masterdata read was successful
#! @result FAILURE: If there was an error while reading the masterdata
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: masterdataread

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formId:
        default: ""
        required: false
    - masterdataname
    - filter:
        default: ""
        required: false

  workflow:
    - masterdata_read:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_READ_MASTERDATA"
            - fninput: ${"{\"filter\":" + enc_json_str(filter) + ",\"masterdataname\":\"" + masterdataname + "\",\"formId\":\"" + formId  + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE