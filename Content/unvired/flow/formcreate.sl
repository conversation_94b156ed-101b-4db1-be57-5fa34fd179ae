#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Creates a new form / formset (and the corresponding task) and assigns it to a team or user
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["formname","comment"]},{"missing_some":[1,["user","team"]]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formname: The (technical) formname to create a submission
#!               $type formname
#!               $label Form To Create
#!               $multiple: false
#!               $defaultSelect forms
#!               $example inspectionform
#!               $required Mandatory
#! @input comment: Helpful notes to fill the form
#!               $type text
#!               $label Comment
#!               $example Related to WO# 21212 from Service desk
#!               $required Mandatory
#! @input recipienttype: The type of recipient
#!               $label Recipient
#!               $type radio
#!               $listvalues User, Team
#!               $default User
#!               $example User
#!               $notes Recipient can be User or Team
#!               $required Mandatory
#!               $generation false
#! @input user:  User (named user who is part of the company/domain) to whom the form needs to be assigned to.
#!               $type user
#!               $label Assign To User
#!               $example <EMAIL>
#!               $defaultSelect users
#!               $multiple false
#!               $required Optional
#!               $notes Either one of user or team needs to be provided
#! @input team: Team to whom the form needs to be assigned to.
#!               $type team
#!               $label Assign To Team
#!               $defaultSelect teams
#!               $example rigteam
#!               $multiple false
#!               $required Optional
#!               $notes Either one of user or team needs to be provided
#! @input formdata: The data to prefill the newly created form with
#!               $type text
#!               $label Prefill Submission Data
#!               $required Optional
#!               $example {"equipment":"Generator"}
#!               $notes Pass in this data to prefill the form with it. When users receive the form it will be prefilled with this data.  Use it to pass in data like equipment (number) to inspect, customer to vist for the sales order etc
#! @input tasktype: The task type for this form, the default is FILL indicating that the task is to fill the form.
#!               $type radio
#!               $label Task Type
#!               $listvalues Fill, Review
#!               $default Fill
#!               $example Fill
#!               $required Optional
#! @input priority: Priority of the task, default no priority is set
#!               $type radio
#!               $label Task Priority
#!               $listvalues High, Medium, Low
#!               $default Low
#!               $example Medium
#!               $required Optional
#! @input duedate: Due date to complete the form, default no due date is set
#!               $type text
#!               $label Due Data (MM/DD/YYYY)
#!               $example MM/DD/YYYY
#!               $required Optional
#! @input extreference: External reference for the task
#!               $type text
#!               $label Unique External Reference
#!               $example xxxxxxxxxx
#!               $required Optional
#!               $notes If an external reference is required (like a Service Order number) can be set. This will also permit the form submission to later be searched based on the reference number
#! @input relatedtask: Provide the related task id if this form is a consequence of the other form
#!               $type text
#!               $label Related Task ID
#!               $example xxxxxxxxxx
#!               $required Optional
#! @input copyfromtaskId: Copy the data from this task / form submission. The created form has a copy of this submission (formdata parameter is ignored in this case).  Not valid for formset
#!               $type text
#!               $label Copy Data From Task ID
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#!
#! @output result: The task ID of the form / formset created
#! @output error: The error from the creation if any
#!
#! @result SUCCESS: If the form / formset was created successfully
#! @result FAILURE: If there was an error while creating the form / formset
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formcreate

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formname
    - comment
    - user:
        default: ""    
        required: false
    - team:
        default: ""    
        required: false
    - formdata:
        default: "{}"    
        required: false
    - relatedtask:
        default: ""    
        required: false
    - tasktype:
        default: ""    
        required: false
    - priority:
        default: ""    
        required: false
    - duedate:
        default: ""    
        required: false
    - extreference:
        default: ""
        required: false
    - copyfromtaskId:
        default: ""    
        required: false

  workflow:
    - form_create:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_CREATE"
            - fninput: ${"{\"formname\":\"" + formname + "\",\"user\":\"" + user + "\",\"team\":\"" + team + "\",\"comment\":\"" + comment + "\",\"formdata\":" + enc_json_str(formdata) + ",\"relatedtask\":\"" + relatedtask + "\",\"tasktype\":\"" + tasktype + "\",\"priority\":\"" + priority + "\",\"duedate\":\"" + duedate + "\",\"extreference\":\"" + extreference + "\",\"copyfromtaskId\":\"" + copyfromtaskId + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE