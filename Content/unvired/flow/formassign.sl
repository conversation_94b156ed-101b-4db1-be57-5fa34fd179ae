#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.
#
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Assign / remove assignment of a form to a user or team
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["comment"]},{"missing_some":[1,["user","team"]]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input comment: Helpful notes for the user/team
#!               $type text
#!               $label Comment
#!               $example Related to WO# 21212 from Service desk
#!               $required Mandatory
#! @input recipienttype: The type of recipient
#!               $label Recipient
#!               $type radio
#!               $listvalues User, Team
#!               $default User
#!               $example User
#!               $notes Recipient can be User or Team
#!               $required Mandatory
#!               $generation false
#! @input user:  User (named user who is part of the company/domain) to whom the form needs to be assigned to.
#!               $type user
#!               $label Assign To User
#!               $defaultSelect users
#!               $example <EMAIL>
#!               $multiple false
#!               $required Optional
#!               $notes Either one of user or team needs to be provided
#! @input team: Team to whom the form needs to be assigned to.
#!               $type team
#!               $label Assign To Team
#!               $defaultSelect teams
#!               $example rigteam
#!               $multiple false
#!               $required Optional
#!               $notes Either one of user or team needs to be provided
#! @input remove: Removes the assignment for a user/team
#!               $type boolean
#!               $label Remove Assignment
#!               $default: false
#!               $example false
#!               $required Optional
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes Set to the newly created form or defaults to ${taskId} for current
#!
#! @output result: The data of the assignment
#! @output error: The error from the assignment if any
#!
#! @result SUCCESS: If the form was assigned successfully
#! @result FAILURE: If there was an error while assigning the form
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formassign

  inputs:
    - alias
    - namespace
    - application
    - convId
    - taskId:
        required: false
    - comment
    - user:
        default: ""
        required: false
    - team:
        default: ""
        required: false
    - remove:
        default: "false"
        required: false

  workflow:
    - assign_form:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_ASSIGN"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"comment\":\"" + comment + "\",\"user\":\"" + user + "\",\"team\":\"" + team + "\",\"remove\":\"" + remove + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE
