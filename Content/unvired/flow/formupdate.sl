#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Updates the (current) form and saves it
#!
#! @prerequisites: {"if":[{"missing":["updateddata"]}]}
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formusers: The users collaborating on the form, will be set automatically
#!               $type system
#!               $required optional
#! @input updateddata: The data to update the form.  Only the fields to update need to be provided
#!               $type text
#!               $label Updated Submission Data
#!               $required Mandatory
#!               $example {"sapNotificationId":"XX12345"}
#!               $notes Pass in the fields to update, other fields are left unmodified
#! @input extreference: External reference for the task
#!               $type text
#!               $label Unique External Reference
#!               $example xxxxxxxxxx
#!               $required Optional
#!               $notes If an external reference is required (like a Service Order number) can be set. This will also permit the form submission to later be searched based on the reference number
#! @input formdata: The current form data.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Submission Data
#!               $required optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes Set to the id to update or ${taskId} for current.  Leave empty for current
#! @input submissionId: Unique ID identifying the form submission (required if the task is for a formset)
#!               $type text
#!               $default ${submissionId}
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes Required if the task is for a formset then the actual form being updated needs to be indicated
#!
#! @output result: The data of the updated form
#! @output error: The error from the update if any
#!
#! @result SUCCESS: If the form was updated successfully
#! @result FAILURE: If there was an error while updating the form
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formupdate

  inputs:
    - alias
    - namespace
    - application
    - convId
    - taskId:
        default: ""
        required: false
    - submissionId:
        default: ""
        required: false
    - updateddata
    - formdata:
        default: "{}"
        required: false
    - formusers:
        default: "{}"
        required: false        

  workflow:
    - form_update:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_UPDATE"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"submissionId\":\"" + submissionId + "\",\"updateddata\":" + enc_json_str(updateddata) + ",\"formdata\":" + enc_json_str(formdata) + ",\"formusers\":" + enc_json_str(formusers) + "}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: extract_result
          - FAILURE: FAILURE

    - extract_result:
        do:
          ops.formresultinternal:
            - alias
            - namespace
            - application
            - convId
            - formparesult: ${result}
        publish:
          -  result: ${stepresult}
          -  error: ${steperror}
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE


  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE