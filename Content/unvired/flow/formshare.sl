#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Create a link to share with external users who do not have a login to the forms system
#!
#! @prerequisites: {"if":[{"missing":[{"if":[{"==":[{"var":"sharetype"},"private"]},["shareto"],[]]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input sharetype: The type of shared link to create.  Public share can be accessed by anyone with the link, private share links are for sharing
#!               with the specified users email
#!               $type radio
#!               $label Sharing Type
#!               $listvalues Public, Private
#!               $example Public
#!               $required Mandatory
#! @input expirydays: The number of days until when the shared link is valid
#!               $type number
#!               $label Days For Expiry
#!               $example 15
#!               $required Mandatory
#!               $notes The expiry will be after the specified days from current date
#! @input createnewshare: By default if there is an existing share URL for this email & taskId then its returned.  If the flag is true then a new share URL is created everytime
#!               $type boolean
#!               $label Create New Share
#!               $default False
#!               $example False
#!               $required Optional
#!               $notes: Defaults to false i.e. if there is an existing share URL for this user &taskId then its returned.
#! @input shareto: Comma separated list of email ids for sharing, required only if share type is private
#!               $type email
#!               $label Email IDs For Sharing
#!               $multiple true
#!               $example <EMAIL>
#!               $required Optional
#!               $notes Required if private share
#! @input draftsave: Allow users to save the form with partly filled data and fill later, required only if share type is private
#!               $type boolean
#!               $label Allow Draft
#!               $default False
#!               $example False
#!               $required Optional
#!               $notes: Defaults to false
#! @input sendshareemail: Should an email with a link be sent?
#!               $type boolean
#!               $label Email Link To Users
#!               $default true
#!               $example true
#!               $required Optional
#!               $notes: Defaults to true
#! @input formdata: The data to prefill the newly created form share with.  This is valid only for a private share of a different form (and not current task)
#!               $type text
#!               $label Prefill Submission Data
#!               $required Optional
#!               $example {"equipment":"Generator"}
#!               $notes Pass in this data to prefill the form with it. When users receive the form it will be prefilled with this data.  Use it to pass in data like equipment (number) to inspect, customer to vist for the sales order etc
#! @input formname: The (technical) formname for sharing,  If specified a new share is created else the current task is shared
#!               $type formname
#!               $label Form Name To Share
#!               $multiple: false
#!               $defaultSelect forms
#!               $example inspectionform
#!               $required Optional
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes Set to the newly created form or ${taskid} for current
#!
#! @output result: Share link on success
#! @output error: The error from the share link creation if any (for e.g. form submission not found)
#!
#! @result SUCCESS: If the creation was successful
#! @result FAILURE: If there was an error while creating the share link
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formshare

  inputs:
    - alias
    - namespace
    - application
    - convId
    - taskId:
        required: false
    - sharetype
    - expirydays
    - formname:
        default: ""    
        required: false
    - formdata:
        default: "{}"    
        required: false
    - draftsave:
        default: 'false'
        required: false
    - shareto:
        default: ""    
        required: false
    - sendshareemail:
        default: 'true'
        required: false
    - createnewshare:
        default: 'false'
        required: false

  workflow:
    - form_share:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_SHARE"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"sharetype\":\"" + sharetype + "\",\"expirydays\":\"" + enc_json_str(expirydays) + "\",\"draftsave\":\"" + enc_json_str(draftsave) + "\",\"sendshareemail\":\"" + enc_json_str(sendshareemail) + "\",\"formname\":\"" + formname + "\",\"formdata\":" + enc_json_str(formdata) + ",\"createnewshare\":\"" + enc_json_str(createnewshare) + "\",\"shareto\":\"" + shareto + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: extract_result
          - FAILURE: FAILURE

    - extract_result:
        do:
          ops.formresultinternal:
            - alias
            - namespace
            - application
            - convId
            - formparesult: ${result}
        publish:
          -  result: ${stepresult}
          -  error: ${steperror}
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE