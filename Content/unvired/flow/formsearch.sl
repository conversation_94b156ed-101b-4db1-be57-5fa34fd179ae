#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Search submissions matching the criteria provided.  At least one of the search criteria is required
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input searchcriteria: Search criteria
#!               $label Search On
#!               $type list
#!               $multiple true
#!               $listvalues Users or Teams, Start Date, End Date, Status, External Reference, Form Name, Form Data
#!               $example Users
#!               $notes Select at least one search criteria
#!               $required Mandatory
#!               $generation false
#! @input completedbyusers:  User(s) who has/have completed the form
#!               $type user
#!               $label Completed By Users
#!               $defaultSelect users
#!               $example <EMAIL>
#!               $multiple true
#!               $required Optional
#!               $notes Either one of users or teams only can be provided
#! @input completedbyteams: Team(s) who has/have completed the form
#!               $type team
#!               $label Completed By Teams
#!               $defaultSelect teams
#!               $example rigteam
#!               $multiple true
#!               $required Optional
#!               $notes Either one of users or teams only can be provided
#! @input submitstartdate: Submissions that were submitted after this date (format based on user's locale)
#!               $type text
#!               $label Submitted After
#!               $example 25/12/2020
#!               $required Optional
#! @input submitenddate: Submissions that were submitted before this date (format based on user's locale)
#!               $type text
#!               $label Submitted Before
#!               $example 25/12/2020
#!               $required Optional
#! @input status: Status of the forms to search for
#!               $type list
#!               $label Submission Status
#!               $listvalues Open, In Progress, Completed, Archived, Failed
#!               $multiple true
#!               $required Optional
#!               $default Completed
#!               $example Completed
#! @input formdata: Search based on form data.  The input is a JSON object of fieldname:value.  Multiple fields can be included.
#!               $type text
#!               $label Submission Data Filter
#!               $example {"equipment":"Generator"}
#!               $required Optional
#! @input extreference: External reference to find submissions
#!               $type text
#!               $label Unique External Reference
#!               $example xxxxxxxxxx
#!               $required Optional
#!               $notes If an external reference is required (like a Service Order number) can be set. This will also permit the form submission to later be searched based on the reference number
#! @input formname: The (technical) formname to find submissions
#!               $type formname
#!               $label Search Form Name
#!               $defaultSelect forms
#!               $example inspectionform
#!               $required Optional
#!               $notes If not provided search will be in the current form
#! @input returnfields: List of comma separated field names to return as part of search fields in addition to the standard search fields
#!               $type text
#!               $label Fields To Return
#!               $example equipment,inspectionnotes
#!               $required Optional
#!               $notes This is valid only if the search is for a specific form as provided in "formname"
#!
#! @output result: JSON array of submissions matching the criteria
#! @output error: The error from the search if any
#!
#! @result SUCCESS: If the search was successful
#! @result FAILURE: If there was an error while performing the search
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formsearch

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formname:
        default: ""
        required: false
    - returnfields:
        default: ""
        required: false
    - completedbyusers:
        default: ""
        required: false
    - completedbyteams:
        default: ""
        required: false
    - submitstartdate:
        default: ""
        required: false        
    - submitenddate:
        default: ""
        required: false        
    - status:
        default: ""
        required: false        
    - formdata:
        default: "{}"
        required: false        
    - extreference:
        default: ""
        required: false        

  workflow:
    - form_search:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_SEARCH"
            - fninput: ${"{\"formname\":\"" + formname + "\",\"returnfields\":\"" + returnfields + "\",\"completedbyusers\":\"" + completedbyusers + "\",\"completedbyteams\":\"" + completedbyteams + "\",\"submitstartdate\":\"" + submitstartdate + "\",\"submitenddate\":\"" + submitenddate + "\",\"extreference\":\"" + extreference + "\",\"status\":\"" + status + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE