#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Setup an Approval Flow of a form 
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input submissionId: Unique ID identifying the form submission (required if the task is for a formset)
#!               $type system
#!               $label Submission ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#! @input approvaldata: The approval levels and groups that have been configured
#!               $type System
#!               $required Mandatory
#!               $notes System managed input.  Will be set when flow is configured
#! @input approvalform: The approval form ID
#!               $type System
#!               $required Mandatory
#!               $notes System managed input.  Will be set when flow is configured
#! @input formusers: The users collaborating on the form, will be set automatically
#!               $type system
#!               $required optional
#! @input approvaltype: Type of approvers
#!               $label Approvers Type
#!               $type list
#!               $multiple false
#!               $listvalues Team, Approvers List, Custom
#!               $example Team
#!               $notes Select one approver type
#!               $required Mandatory
#!               $generation false
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $required Optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The approval needs to operate on the provided formdata
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes This is typically the taskId available in the current flow (set to $taskId} for the form being processed
#!               $notes or the result of a create form flow call
#!
#! @output result: Result of the approval
#! @output error: The error if any while processing the approval
#!
#! @result APPROVED: If the approval is completely approved
#! @result REJECTED: If the approval is rejected
#! @result RETURNED: If the approval is returned to requestor
#! @result CONTINUE: If the approval cycle has to still continue
#! @result FAILURE: If there was an error while approving
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: approvalworkflow

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata
    - formusers:
        default: "{}"
        required: false
    - approvaldata
    - approvalform
    - taskId:
        default: ""
        required: false
    - submissionId:
        default: ""
        required: false

  workflow:
    - approval_workflow:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_APPROVAL"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"submissionId\":\"" + submissionId + "\",\"formdata\":" + enc_json_str(formdata) + ",\"approvaldata\":" + enc_json_str(approvaldata) + ",\"approvalform\":\"" + approvalform + "\"}"}
        publish:
          - result 
          - error
        navigate:
          - SUCCESS: check_approval
          - FAILURE: FAILURE

    - check_approval:
        do:
          ops.formapprovaldecision:
            - alias
            - namespace
            - application
            - convId
            - approvalresult: ${result}
        publish:
          -  result: ${stepformdata}
          -  error: ${steperror}
        navigate:
          - APPROVED: APPROVED
          - REJECTED: REJECTED
          - RETURNED: RETURNED
          - CONTINUE: CONTINUE
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - APPROVED
    - REJECTED
    - RETURNED
    - CONTINUE
    - FAILURE
