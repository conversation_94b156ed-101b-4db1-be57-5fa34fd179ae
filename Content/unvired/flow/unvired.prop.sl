namespace: unvired.flow
properties:
 - ads.systemtype: ADS
 - ads.server: some servr
 - ads.port: '2500'
 - ads.authenticationtype: Simple
 - demodb.systemtype: DB
 - demodb.dbtype: mysql
 - demodb.server: localhost
 - demodb.port: '3306'
 - demodb.user: demouser
 - demodb.password: unvired
 - demodb.database: forms_demo
 - formsdemo.systemtype: DB
 - formsdemo.dbtype: mysql
 - formsdemo.server: **************
 - formsdemo.port: '3306'
 - formsdemo.user: umpuser
 - formsdemo.password: Unvired123*
 - formsdemo.database: forms_demo
 - msnwodata.systemtype: ODATA
 - msnwodata.url: https://services.odata.org/
 - msnwodata.servicepath: /V3/Northwind/Northwind.svc
 - msnwodata.servicetype: Others
 - msnwodata.authenticationtype: None
 - rest2.systemtype: REST
 - rest2.url: urlfield
 - rest2.authenticationtype: Basic
 - rest2.oauthprovider: box
 - restsystem.systemtype: REST
 - restsystem.url: someurl
 - restsystem.authenticationtype: Basic
 - restsystem.oauthprovider: google