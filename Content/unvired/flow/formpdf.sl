#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Export the form to PDF
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input orientation: The orientation for generating the PDF
#!               $type radio
#!               $label PDF Orientation
#!               $listvalues Portrait, Landscape
#!               $default Portrait
#!               $example Portrait
#!               $required Optional
#! @input header: The PDF header
#!               $label PDF Header
#!               $type text
#!               $example Header HTML string
#!               $required Optional
#! @input footer: The PDF footer
#!               $label PDF Footer
#!               $type text
#!               $example Footer HTML string
#!               $required Optional
#! @input resultformat: The format to return the attachment id(s)
#!               $type radio
#!               $label Output Format
#!               $listvalues CSV, JSON
#!               $default CSV
#!               $example CSV
#!               $required Optional
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes This is typically the taskId available in the current flow (set to $taskId} for the form being processed
#!               $notes or the result of a create form flow call
#! @input submissionId: Unique ID identifying the form submission (required if the task is for a formset)
#!               $type text
#!               $label Submission ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#! @input failonerror: Fail the flow on error
#!               $type boolean
#!               $label Fail flow On Error
#!               $default true
#!               $example true
#!               $notes If this is a non-critical step in the flow, failonerror can be set to false so that the flow will continue
#!               $notes to execute the next step
#!               $required Optional
#!
#! @output result: PDF File ID on success
#! @output error: The error from the PDF creation if any (for e.g. form submission not found)
#!
#! @result SUCCESS: If the export was successful
#! @result FAILURE: If there was an error while creating the PDF version
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: formpdf

  inputs:
    - alias
    - namespace
    - application
    - convId
    - taskId:
        required: false
    - submissionId:
        required: false
    - orientation:
        default: "portrait"
        required: false
    - header:
        default: ""
        required: false
    - footer:
        default: ""
        required: false
    - resultformat:
        default: "csv"
        required: false
    - failonerror:
        default: 'true'
        required: false

  workflow:
    - form_pdf:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_FORM_PDF"
            - fninput: ${"{\"taskId\":\"" + taskId + "\",\"submissionId\":\"" + submissionId + "\",\"orientation\":\"" + orientation + "\",\"resultformat\":\"" + resultformat + "\",\"failonerror\":\"" + failonerror + "\",\"header\":\"" + header + "\",\"footer\":\"" + footer + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE