#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Add, Modify or delete a single Master data record
#!
#! @prerequisites: {"if":[{"missing":["data"]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formId: Unique ID identifying this master data form
#!               $type system
#!               $example xxxxxxxxx
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input masterdataname: The Masterdata entiry to update
#!               $type masterdataname
#!               $label Master Data Name
#!               $multiple: false
#!               $defaultSelect forms
#!               $example equipment
#!               $required Mandatory
#! @input operation: The action to perform with the external user
#!               $type radio
#!               $label Action
#!               $listvalues Update, Delete
#!               $default Update
#!               $example Update
#!               $required Mandatory
#!               $notes Update operation works similar to upsert, ie. update if existing else insert a new record
#! @input data: A single Master data record
#!               $type text
#!               $label Master Data To Save
#!               $example {"equipmentId":"111", "equipmentName": "Laptop"}
#!               $required Mandatory
#!               $notes Master data already cached will be updated with this data
#!
#! @output result: Message that all master data is updated successfully
#! @output error: The error while performing the masterdata operations
#!
#! @result SUCCESS: If the master data updated successfully
#! @result FAILURE: If there was an error while performing the required action on the master data
#!!#
############################################################################################################################################
namespace: unvired.flow
imports:
  ops: unvired.operation
  flows: unvired.flow

flow:
  name: masterdataupdate

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formId:
        default: ""
        required: false
    - masterdataname
    - operation
    - data

  workflow:
    - masterdata_update:
        do:
          ops.execjava:
            - alias
            - namespace
            - application
            - convId
            - function: "DIGITAL_FORMS_PA_WF_UPDATE_MASTERDATA_RECORD"
            - fninput: ${"{\"data\":" + enc_json_str(data) + ",\"masterdataname\":\"" + masterdataname + "\",\"operation\":\"" + operation + "\"}"}
        publish:
          - result
          - error
        navigate:
          - SUCCESS: SUCCESS
          - FAILURE: FAILURE

  outputs:
    - result
    - error

  results:
    - SUCCESS
    - FAILURE