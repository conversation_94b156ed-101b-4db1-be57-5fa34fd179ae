#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any REST request on a remote server. The authentication for the endpoint is provided via the SystemProperty file
#!
#! @prerequisites: {"if":[{"missing":["system","contenttype","accept","httpverb"]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The REST system endpoint definition
#!               $type list
#!               $label API Server
#!               $example dropbox
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input httpverb: The HTTP Action (verb) to perform
#!               $type list
#!               $label HTTP Action
#!               $listvalues Get, Post, Put, Patch, Delete
#!               $default Get
#!               $example Get
#!               $notes The HTTP verb or action to perform
#!               $required Mandatory
#! @input contenttype: The content type
#!               $type list
#!               $label Content Type
#!               $listvalues text/plain, application/json, application/xml, application/x-www-form-urlencoded, multipart/form-data
#!               $default application/json
#!               $example application/json
#!               $notes The content type for the HTTP call
#!               $required Mandatory
#! @input accept: The accept type
#!               $type list
#!               $label Accept
#!               $listvalues application/json, application/xml
#!               $default application/json
#!               $example application/json
#!               $notes The accept type for the HTTP call
#!               $required Mandatory
#! @input urlpath: The (url) path for the call
#!               This is usually referred to as the instanceUrl and can be set to override the system properties
#!               $type text
#!               $label URL Path
#!               $example /login
#!               $notes The urlPath is appended to the configured URL or to the instanceURL specified
#!               $required Optional
#! @input headerparameters: Comma separated list of header parameters to pass to this call 
#!               $type keyvalue
#!               $label Header Params
#!               $example "auth-token" will add one header parameter auth-token to the final call
#!               $notes If headerparameters is set to an empty string, no header parameter will be included.
#!               $required Optional
#! @input postparameters: Comma separated list of post parameters to pass to this call 
#!               $type keyvalue
#!               $label Post Params
#!               $example  "firstname, lastname" will add two post parameters firstname and lastname to the final call
#!               $notes If postparameters is set to an empty string, no post parameter will be included.
#!               $required Optional
#! @input body: Data to be posted as raw body to the endpoint 
#!               $type text
#!               $label Body Data
#!               $example {"key": "some value that needs to be posted to the webhook in the body of the call:}
#!               $notes The specified content is written to the body of the request call
#!               $required Optional
#! @input postquerystring: Indicates if the post parameters also have to be sent as a query string
#!               $type boolean
#!               $label Post Query
#!               $default False
#!               $example False
#!               $notes The default value is False if this parameter is not specified
#!               $required Optional
#! @input attachments: Comma separated list of Attachment IDs of the files to be posted to the end point.  The attachment IDs are obtained by uploading the files to the Unvired Platform using the attachmentrequest flow
#!               $type text
#!               $label Unique Attachment IDs
#!               $example ATT123456, ATT789012
#!               $notes The attachments already need to be uploaded before posting to external service
#!               $required Optional
#! @input httpsuccesscodes: Fail the flow if the return code from the HTTP request is not one of the following
#!               $type text
#!               $label Success HTTP Codes
#!               $example 200,201,204
#!               $notes Enter the comma separated list of http codes that the flow needs to check for success, any other return code will be treated as an error
#!               $notes If this parameter is provided then failonerror will be ignored and in case of an error the flow will fail.
#!               $required Optional
#! @input instanceurl: With some services like Salesforce, ServiceNow etc, the URL is dynamic and may need to be changed from what is configured in the system properties
#!               This is usually referred to as the instanceUrl and can be set to override the system properties
#!               $type text
#!               $label Instance URL (If Dynamic)
#!               $example https://na14.salesforce.com
#!               $notes The default value is the URL configured in the system properties if this parameter is not specified
#!               $required Optional
#! @input failonerror: Fail the flow on error
#!               $type boolean
#!               $label Fail flow On Error
#!               $default true
#!               $example true
#!               $notes If this is a non-critical step in the flow, failonerror can be set to false so that the flow will continue
#!               $notes to execute the next step
#!               $required Optional
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Submission Data If Different
#!               $required Optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#!
#! @output httpcode: The http return code
#! @output headers: The headers returned from the hthp call
#! @output body: The data returned from the http call
#! @output result: The consolidated result of the HTTP execution
#! @output error: The error from the HTTP execution if any
#!
#! @result SUCCESS: If the function executed successfully
#! @result FAILURE: If there was an error while executing the function
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: http

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata:
        required: false
    - system
    - contenttype
    - accept
    - httpverb
    - postquerystring:
        required: false
    - instanceurl:
        required: false
    - urlpath:
        required: false
    - attachments:
        required: false
    - headerparameters:
        required: false
    - postparameters:
        required: false        
    - body:
        required: false
    - failonerror:
        default: 'true'
        required: false
    - httpsuccesscodes:        
        required: false
        
  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.HttpRequest
    method_name: execute

  outputs:
    - httpcode: ${httpcode}
    - headers: ${headers}
    - resultbody: ${resultbody}
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE