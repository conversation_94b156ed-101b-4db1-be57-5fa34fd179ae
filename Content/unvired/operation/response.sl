#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  A simple operation that publishes (injects) the result and error to that passed in
#!
#! @prerequisites: {"missing_some":[1,["injectresult","injecterror"]]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation#!
#! @input responsetype: Success or Error response
#!               $label Response
#!               $type radio
#!               $listvalues Success, Failure
#!               $default Success
#!               $required Mandatory
#!               $example Success
#!               $notes Return a success or failure from the flow
#! @input httpcode: Response code to return
#!               $type text
#!               $label HTTP Code
#!               $default 201
#!               $required Mandatory
#!               $notes The HTTP response code to return, useful if response needs to be
#!               $notes to be customized in a flow.
#! @input body: Response to return
#!               $type text
#!               $label Body
#!               $required Mandatory
#!               $notes The body value is published as final result, useful if response needs to be
#!               $notes to be customized in a flow.
#! @input headers: Headers to return
#!               $type keyvalue
#!               $label Headers
#!               $required Optional
#!               $notes Key Value pairs to be returned as headers for the HTTP response.
#!
#! @output result: The result body
#! @output error: The error body
#!
#! @result SUCCESS: If the error is empty
#! @result FAILURE: If the error is not empty
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: response

  inputs:
    - alias
    - namespace
    - application
    - convId
    - responsetype
    - body
    - httpcode:
        default: "201"
        required: true
    - headers:
        default: ""
        required: false

  python_action:
    script: |
      try:
        import json
        final_result = {}
        response_result = {}

        # Check if body is a json or not
        try:
          body_obj = json.loads(body)
          response_result['body'] = body_obj
        except ValueError:
          response_result['body'] = body
        
        response_result['httpcode'] = httpcode

        # Now handle the delimited headers
        if headers:
          parts = headers.split("~|~")
          headers_result = []

          for part in parts:
            key_value = part.split("|~|")
            headers_result.append({key_value[0]: key_value[1]})
          response_result['headers'] = headers_result
        else:
          response_result['headers'] = []

        final_result['wf_response'] = response_result

        finalrslt = json.dumps(final_result)
        finaltype = responsetype

      # Handle exceptions so that errors in the custom script are also caught
      except Exception as e:
        finalrslt = "Error creating response" + str(e)
        finaltype = "failure"
        
  outputs:
      - result: ${finalrslt if finaltype=='success' else ''}
      - error: ${finalrslt if finaltype=='failure' else ''}

  results:
    - SUCCESS: ${finaltype=="success"}
    - FAILURE