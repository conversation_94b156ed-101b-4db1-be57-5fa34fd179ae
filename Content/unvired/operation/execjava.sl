#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any Unvired Application Function (or Process Agent Java Function)
#!
#! @prerequisites: {"if":[{"missing":["function"]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input function: The logical name of the function to execute as configured in the Unvired Application
#!               $type text
#!               $label Function Name
#!               $example PA_DIGITAL_FORMS_GET_FORMS
#!               $required Mandatory
#!               $notes These are Java functions that have been created using the Unvired Modeller and use the Unvired SDK
#!               $notes to interface with SAP S4/HANA, Salesforce, Databases and any REST/SOAP backend systems.
#! @input fninput: The additional input for the function specified above
#!               $type keyvalue
#!               $label Input To Function
#!               $example {"customer":"unvired"}
#!               $notes If this input is not specified, ALL Post parameters (except system defined ones)
#!               $notes which are posted to the executeWorkflow REST API will be passed to the function as input
#!               $notes If using the input directly this should be a well formed JSON as defined in the Unvired Modeller.  
#!               $required Optional
#!
#! @output result: The result of the function execution
#! @output error: The error from the function execution if any
#!
#! @result SUCCESS: If the function executed successfully
#! @result FAILURE: If there was an error while executing the function
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: execjava

  inputs:
    - alias
    - namespace
    - application
    - convId
    - function
    - fninput:
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.PARequest
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE