#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  INTERNAL: Execute any SubFlow asynchronously in parallel or one at a time
#!
#! @prerequisites: {"if":[{"missing":["function"]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input submissionId: Unique ID identifying the form submission (required if the task is for a formset)
#!               $type system
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formusers: The users collaborating on the form, will be set automatically
#!               $type system
#!               $required optional
#! @input flowname: The flow to execute
#!               $type text
#!               $example createsfdclead
#!               $required Mandatory
#! @input flowinput: The additional input for the flow specified above
#!               $type keyvalue
#!               $example {"customer":"unvired"}
#!               $required Mandatory
#! @input isparallel: Execute the job in parallel or in sequence one at a time
#!               $type text
#!               $example true
#!               $required Mandatory
#! @input taskId: Unique ID identifying the task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes This is typically the taskId available in the current flow (set to $taskId} for the form being processed
#!               $notes or the result of a create form flow call
#! @input formdata: The currently saved form data.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $required optional
#!
#! @output result: The result of the function execution
#! @output error: The error from the function execution if any
#!
#! @result SUCCESS: If the function executed successfully
#! @result FAILURE: If there was an error while executing the function
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: asyncsubflow

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formId
    - taskId:
        required: false
    - submissionId:
        required: false
    - flowinput:
        required: false
    - formdata:
        required: false
    - formusers:
        required: false
    - flowname
    - isparallel

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.AsyncSubFlow
    method_name: execute

  outputs:
    - finalResult: ${result}
    - finalError: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE