#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute an SAP BAPI or RFC
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The SAP system name to use.
#!               $type list
#!               $label SAP Server
#!               $example ecc
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input operation: Specify the operation to perform
#!               $label RFC Operation
#!               $type list
#!               $listvalues Begin Session, Execute, End Session
#!               $default Execute
#!               $example Execute
#!               $notes If session handling is required across multiple RFC or BAPI calls in a flow, you can use Begin Session
#!               $notes and EndSession to execute all in a session
#!               $required Mandatory
#! @input function: The function name to execute
#!               $type text
#!               $label RFC Or BAPI Function Name
#!               $example BAPI_CUSTOMER_GETDETAIL2;
#!               $notes Provide the name of the BAPI or RFC to execute on the SAP system
#!               $required Optional
#! @input parameters: The input and output parameters as JSON for the specified function
#!              $type text
#!              $label Function Parameters
#!              $example {"RFCInput":["CUSTOMERID"], "RFCOutput":["CUSTOMER","RETURN"], "RFCParameter":{}}
#!              $notes The input and output parameters for the BAPI/RFC
#!              $required Optional
#! @input maptype: The type of mapping
#!               $label Mapping Type
#!               $type radio
#!               $listvalues Default, Manual
#!               $default Default
#!               $example Default
#!               $required Optional
#!               $generation false
#! @input rfcinputs: The mapped inputs for the function specified above
#!               $type keyvalue
#!               $label Inputs
#!               $example {"customer":"unvired"}
#!               $notes Used by system
#!               $required Optional
#!               $generation false
#! @input rfcoutputs: The mapped outputs for the function specified above
#!               $type keyvalue
#!               $label Outputs
#!               $example {"customer":"unvired"}
#!               $notes Used by system
#!               $required Optional
#!               $generation false
#! @input data: The data as JSON for the specified function
#!              $type text
#!              $label Data To Process
#!              $example {"CUSTOMERID":"001000"}
#!              $notes The input data for the BAPI/RFC
#!              $required Optional
#! @input transaction: Whether SAP transaction handling to be included or not
#!               $type radio
#!               $label Enable Transaction
#!               $listvalues Yes, No
#!               $example No
#!               $default No
#!               $required Optional
#!
#! @output result: The result of the SAP operation executed
#! @output error: The error from the SAP operation if any
#!
#! @result SUCCESS: If the operation executed successfully
#! @result FAILURE: If there was an error while executing the operation
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: sap

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - operation
    - function:
        default: ""
        required: false
    - parameters:    
        default: ""
        required: false
    - data:
        default: ""
        required: false
    - transaction:
        default: "No"
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.SAPRequest
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE