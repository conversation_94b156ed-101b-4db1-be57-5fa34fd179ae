#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Run a SQL query on a database or insert/update/delete data directly using JSON input
#!
#! @prerequisites: {"if":[{"missing":["system"]},{"missing_some":[1,["operation","query"]]},["operation","query"],{"merge":[{"missing_some":[1,{"if":[{"var":"operation"},[],["query"]]}]},{"missing_some":[3,{"if":[{"var":"query"},[],["operation","keys","data"]]}]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $label Alias
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $label Namespace
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $label Application 
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $label Conversation ID
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Database system name to use.
#!               $label Database Server
#!               $type list
#!               $example forms-db
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input exectype: The type of execution
#!               $label Execution Type
#!               $type radio
#!               $listvalues Query, Operation
#!               $default Query
#!               $example query
#!               $notes Query to execute a native query or Operation to execute a predetermined SQL operation
#!               $required Mandatory
#!               $generation false
#! @input query: The query to execute on the database.
#!               $label SQL Query
#!               $type text
#!               $example select * from customer;
#!               $notes The query can be any valid Select, Insert, Update or Delete SQL query.  If multi-query support is enabled, multiple queries separated by ; can also be executed
#!               $notes IMPORTANT: Use single-quote ' to enclose form fields (as fields javing JSON data will have ") such as update xyz set cola='${field}';
#!               $notes Either query or operation has to be specified for a valid execution
#!               $required Optional
#! @input operation: Instead of specifying the SQL statement, an operation can be specified here and JSON data passed in to update in the database.  
#!               $label SQL Operation
#!               $type list
#!               $listvalues Insert, Update, Upsert, Delete
#!               $default Insert
#!               $example Insert
#!               $notes In this example the statement to be executed is insert  and the specified data will be inserted into the table
#!               $notes Either query or operation has to be specified for a valid execution
#!               $notes upsert is only supported on MySQL (currently) and performs an insert or update (if row exists)
#!               $required Optional
#! @input keys: The keys of the table(s) for the specified operation  
#!              $label Primary Keys
#!              $type text
#!              $example {"keys":{"salesOrder":"orderID", "orderItem":["pkID","fkIDOrder"]}}
#!              $notes In this example the operation will be performed with salesOrder table having orderID as primary key,
#!              $notes orderItem table having pkID as the primary key and fkIDOrder as the foreign key
#!              $notes Required parameter if operation is specified instead of query
#!              $required Optional
#! @input data: The JSON data array to be persisted in the database for the specified operation, should be a JSON Array of objects
#!              $label Data To Process
#!              $type text
#!              $example [{"salesOrder":{"orderId":12,"customer":"CPB1000100","deliveryDate":"2020-01-28"},"orderItem":[{"fkIDOrder":12,"pkID":"10","matNumber":"CPB11600","quantity":10,"unit":"CSE"},{"fkIDOrder":12,"pkID":"20","matNumber":"CPB11100","quantity":2,"unit":"L"}]}]
#!              $notes The above input will result in data for salesOrder table and columns orderId, customer and deliveryDate and two rows        
#!              $notes of data for orderItem table with fkIDOrder the foreign key, pkID the primary key and columns matNumber
#!              $notes quantity and unit (of measure)
#!              $notes Required parameter if operation is specified instead of query
#!              $required Optional
#! @input flattenoutput: Flatten output if single row of data
#!               $label Flatten Output
#!               $type boolean
#!               $default false
#!               $example false
#!               $required Optional
#!               $notes If the output of the query is a single row, then return the data as a JSON object instead of as an Array of objects
#! @input errormessageonnodata: Error message on data not found
#!               $label No Data Found Error
#!               $type text
#!               $required Optional
#!               $example Equipment not found
#!               $notes When no data is found for a database query, if errormessageonnodata is not empty, the operation is treated
#!               $notes as an error and the same is returned in the error result
#!
#! @output result: The result of the database operation executed
#! @output error: The error from the database operation if any
#!
#! @result SUCCESS: If the operation executed successfully
#! @result FAILURE: If there was an error while executing the operation
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: query

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - query:
        required: false
    - operation:
        required: false        
    - keys:
        required: false        
    - data:
        required: false
    - flattenoutput:
        required: false
    - errormessageonnodata:
        default: ""
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.DBRequest
    method_name: query

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE