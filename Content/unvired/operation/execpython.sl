#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any adhoc Python script
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input contentpath: The dynamic content path
#!               $type system
#!               $required Mandatory
#!               $example /opt/UMP5/UMP_CONTENT/content
#!               $notes System managed input, will be automatically generated on instance
#! @input scriptname: The name of the script to execute
#!               $type system
#!               $required Mandatory
#!               $example createjson
#!               $notes System managed input, will be automatically set to stepname
#! @input inputdata: Python dictionary (string representation) of all the chosen inputs
#!               $type text
#!               $label Data To Process
#!               $example {"file":"dropbox"}
#!               $notes The selected inputs are all packaged as a dictionary and passed to the script
#!               $required Mandatory
#!
#! @output result: The result of the script execution
#! @output error: The error from the script execution if any including exceptions
#!
#! @result SUCCESS: If the script executed successfully
#! @result FAILURE: If there was an error while executing the script
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: execpython

  inputs:
    - alias
    - namespace
    - application
    - convId
    - contentpath
    - scriptname
    - inputdata

  python_action:
    script: |
      try:
        from com.unvired.ump.agent import WorkflowLogManager
        from com.unvired.ump.agent import IWorkflowLogger

        import sys, json, base64

        logger = WorkflowLogManager.getLogger(convId)
        logger.info("execpython - Executing: " + scriptname + " from: " + contentpath)

        # Add to syspath if already not added
        if (contentpath not in sys.path):
          sys.path.append(contentpath)

        logger.debug("execpython - Loading class")
        mod = __import__(alias.lower(), fromlist=[scriptname])
        execclass = getattr(mod, scriptname)

        logger.debug("execpython - Refreshing cache")
        reload(execclass)

        logger.debug("execpython -inputdata: " + inputdata)

        logger.info("execpython - Processing inputdata for BASE64")
        processedInput = {}
        jsonobj = json.loads(inputdata)
        for attrib,value in jsonobj.items():
          if "BASE64://" in value:
            if value.startswith('"') and value.endswith('"'):
              value = value[1:len(value)]
            value = value[9:]
            valuebytes = value.encode('ascii')
            valuejsonbytes = base64.b64decode(valuebytes)
            value = valuejsonbytes.decode('utf-8')
          processedInput[attrib] = value
        inputdata = json.dumps(processedInput)

        logger.debug("execpython - processed inputdata: " + inputdata)
  
        logger.info("execpython - Executing script")
        retvals = execclass.execute(inputdata, logger)
        
        # All returns need to be stringified
        result = json.dumps(retvals)
        logger.debug("execpython - Result: " + result)

        # Error needs to be retrieved from the result dictionary
        error = retvals['error'] if ('error' in retvals) else str('')

      # Handle exceptions so that errors in the custom script are also caught
      except Exception as e:
        error = "Adhoc Python script: " + scriptname + " had an error: " + str(e)
        logger.error("execpython exception : " + error)

        # Error needs to be returned as a dictionary
        result = {"error": error}
        
  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE
