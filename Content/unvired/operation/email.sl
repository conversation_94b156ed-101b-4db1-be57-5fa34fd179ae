#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Send an email to one or more recipients as part of this flow.
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["subject","message"]},{"missing_some":[1,["users","teams","emails"]]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input submissionId: Unique ID identifying the form submission (required if the task is for a formset)
#!               $type system
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formusers: The users collaborating on the form, will be set automatically
#!               $type system
#!               $required optional
#! @input subject: The email subject
#!               $type text
#!               $label Email Subject
#!               $example Related to WO# ${number} from Service desk
#!               $required Mandatory
#!               $notes Subject can include ${formfields} that will be replaced with actual values from the form data before sending
#! @input message: The email message
#!               $type textarea
#!               $label Email Message
#!               $example Related to WO# ${number} from Service desk
#!               $required Mandatory
#!               $notes Message can include ${formfields} that will be replaced with actual values from the form data before sending
#! @input recipienttype: The type of recipients
#!               $label Recipients
#!               $type radio
#!               $listvalues Users, Teams, Emails
#!               $default Users
#!               $example Users
#!               $notes Recipients can be Users, Teams or custom Email Ids
#!               $required Mandatory
#!               $generation false
#! @input users: Users (named users who are part of the company/domain) to whom the email needs to be sent to.
#!               $type user
#!               $label Recipient Users
#!               $multiple true
#!               $defaultSelect users
#!               $example <EMAIL>
#!               $required Optional
#! @input teams: Teams to whom the email needs to be sent to.
#!               $type team
#!               $label Recipient Teams
#!               $multiple true
#!               $defaultSelect teams
#!               $example rigteam
#!               $required Optional
#! @input emails: Email IDs of external users to whom the email needs to be sent to.
#!               $type email
#!               $label Recipient Emails
#!               $multiple true
#!               $example <EMAIL>
#!               $required Optional
#! @input attachmentids: Attachment IDs of files to include in the email.
#!               $type text
#!               $label Unique Attachment IDs
#!               $example XXXXXXXX
#!               $required Optional
#!               $notes These are IDs typically of PDF exports or other files that have been uploaded and are to be attached to the email
#! @input template: The email template to use
#!               $type list
#!               $label Email Template
#!               $listvalues GENERAL_MAIL
#!               $required Optional
#!               $default GENERAL_MAIL
#! @input taskId: Unique ID identifying the task, provide this if the alert is not related to the current task
#!               $type text
#!               $label Task ID If Different
#!               $example XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Optional
#!               $notes This is typically the taskId available in the current flow for the form being processed
#!               $notes or the result of a create form flow call
#! @input failonerror: Fail the flow on error
#!               $type boolean
#!               $label Fail flow On Error
#!               $default true
#!               $example true
#!               $notes If this is a non-critical step in the flow, failonerror can be set to false so that the flow will continue
#!               $notes to execute the next step
#!               $required Optional
#! @input formdata:  The input form data to use for email substitution.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Submission Data If Different
#!               $required Optional
#!               $example {"equipment":"Generator"}
#!               $notes Pass in this data to prefill the form with it. When users receive the form it will be prefilled with this data.  Use it to pass in data 
#!               $notes like equipment (number) to inspect, customer to vist for the sales order etc
#!
#! @output result: Empty string on success
#! @output error: The error from the email send if any (for e.g. user or team not found)
#!
#! @result SUCCESS: If the send was successful
#! @result FAILURE: If there was an error while sending the email
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: email

  inputs:
    - alias
    - namespace
    - application
    - convId
    - taskId:
        required: false
    - submissionId:
        required: false
    - formdata:
        required: false
    - formusers:
        required: false
    - users:
        required: false
    - teams:
        required: false
    - emails:
        required: false
    - subject
    - message
    - attachmentids:
        required: false
    - template:
        default: 'GENERAL_MAIL'
        required: false
    - failonerror:
        default: 'true'
        required: false
            
  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.SendEmail
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE