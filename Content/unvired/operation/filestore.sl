#   (c) Copyright 2022 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Upload/Download or Delete a file to/from the attachment cache in the requested format
#!
#! @prerequisites: {"missing_some":[1,{"merge":[{"if":[{"in":[{"var":"operation"},["Delete","Download"]]},["attachmentid","externalreference"],[]]},{"if":[{"==":[{"var":"operation"},"Upload"]},["data"],[]]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input operation: The action to perform on the cache.
#!               $type list
#!               $label Operation
#!               $listvalues  Upload, Download, Delete
#!               $default Upload
#!               $example Upload
#!               $required Mandatory
#! @input format: The data format of the input data or return data
#!               $type radio
#!               $label Input-Output File Format
#!               $listvalues BASE64, Text
#!               $default BASE64
#!               $example BASE64
#!               $required Mandatory
#! @input filename: The filename of the file being added to cache
#!               $type text
#!               $label Attachment File Name
#!               $example somefile.doc
#!               $required Optional
#!               $notes  Required for operation Upload
#! @input attachmentid: Unique ID identifying the attachment to retrieve
#!               $type text
#!               $label Unique Attachment Id
#!               $example ATT11111111111
#!               $required Optional
#!               $notes This is typically the attachment ID returned from a flow step that converts form to PDF or
#!               $notes or the attachments that are part of the submitted form etc
#!               $notes Either external reference or attachmentID needs to be provided for Download and Delete operations
#! @input data: The contents of the file being added to cache
#!               $type text
#!               $label File Contents To Save
#!               $example somefile.doc
#!               $required Optional
#!               $notes  Required for operation Upload
#! @input mimetype: The mime type of the file to upload
#!               $type text
#!               $label File Mime Type
#!               $example image/jpeg
#!               $required Optional
#!               $notes  Can be provided for operation Upload
#! @input externalreference: Unique external ID identifying the attachment to retrieve
#!               $type text
#!               $label Unique External Reference
#!               $example ATT11111111111
#!               $required Optional
#!               $notes Either external reference or attachmentID needs to be provided for Download and Delete operations
#!
#! @output result: Attachment data in the requested format or attachment ID if uploaded
#! @output error: The error if any
#!
#! @result SUCCESS: If the read/write of attachment from/to cache was successful
#! @result FAILURE: If there was an error while processing the attachment cache
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: filestore

  inputs:
    - alias
    - namespace
    - application
    - convId
    - operation
    - data:
        required: false    
    - filename:
        required: false    
    - mimetype:
        required: false    
    - attachmentid:
        required: false
    - externalreference:
        required: false
    - format:
        default: "base64"
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.FileCacher
    method_name: cache

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE