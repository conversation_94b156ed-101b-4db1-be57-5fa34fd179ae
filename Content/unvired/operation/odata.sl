#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any OData request on a remote server. The authentication for the endpoint is provided via the SystemProperty file
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["system","metadata","operation","entity"]},{"missing_some":[1,{"if":[{"==":[{"var":"operation"},"Read"]},["entitydata"],[]]}]},{"missing_some":[1,{"if":[{"==":[{"var":"operation"},"Create"]},["entitydata"],[]]}]},{"missing_some":[1,{"if":[{"==":[{"var":"operation"},"Update"]},["entitydata"],[]]}]},{"missing_some":[1,{"if":[{"==":[{"var":"operation"},"Merge"]},["entitydata"],[]]}]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The OData system endpoint definition
#!               $type list
#!               $label OData Server
#!               $example dropbox
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input operation: The operation to perform
#!               $type list
#!               $label OData Operation
#!               $listvalues Create, Update, Merge, Delete, Select, Read
#!               $default Select
#!               $example Select
#!               $notes The OData operation to perform
#!               $required Mandatory
#! @input entity: Entity for the operation
#!               $type list
#!               $label OData Entity
#!               $example customer
#!               $notes The main entity to perform the operation specified
#!               $required Mandatory
#! @input fields: The fields to return
#!               $label Fields
#!               $type list
#!               $required Mandatory
#!               $generation false
#! @input metadata: Metadata of Entities and fields to retrieve/save as part of this operation
#!               $type text
#!               $label OData Metadata
#!               $example [{"entitySet":"Customers","entityType":"customer","keys":["CustomerID"],"fields":[{"name":"CustomerID","type":"String","nullable":false},{"name":"CompanyName","type":"String","nullable":false}],"navigation":["Orders","CustomerDemographics"]}]
#!               $notes The entities required can be mapped from the OData metadata
#!               $required Mandatory
#! @input entitydata: The entity to process
#!               $type text
#!               $label Entity Data
#!               $example {"customerId":"12345", "CompanyName":"Acme Corporation"}
#!               $notes The data to write.  Not required for Select Operation
#!               $required Optional
#! @input expandtype: The type of mapping for expand
#!               $label Expand Mapping Type
#!               $type radio
#!               $listvalues Default, Manual
#!               $default Default
#!               $example Default
#!               $required Optional
#!               $generation false
#! @input expand: Related entities to expand
#!               $type list
#!               $label Expand Entity
#!               $example $expand=Customer
#!               $notes Refer OData documentation to provide the filter https://www.odata.org/getting-started/basic-tutorial/#expand
#!               $required Optional
#! @input filter: For Select the filter to use on result
#!               $type text
#!               $label Filter Expression
#!               $example field eq abc
#!               $notes Refer OData documentation to provide the filter https://www.odata.org/getting-started/basic-tutorial/#filter
#!               $required Optional
#! @input parameters: Provide additional standard parameters for the OData call such as $count, $top etc
#!               $type keyvalue
#!               $label OData Parameters
#!               $example {"$count": true}
#!               $notes Refer OData documentation to provide the filter https://www.odata.org/getting-started/basic-tutorial/#count
#!               $required Optional
#! @input errormessageonnodata: Error message on data not found
#!               $type text
#!               $label No Data Found Error
#!               $required Optional
#!               $example Customer not found
#!               $notes When no data is found for a OData Select or Read (for e.g.), if errormessageonnodata is not empty, the operation is treated
#!               $notes as an error and the same is returned in the error result
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Updated Submission Data
#!               $required Optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#!
#! @output result: The result of the OData execution
#! @output error: The error from the OData execution if any
#!
#! @result SUCCESS: If the function executed successfully
#! @result FAILURE: If there was an error while executing the function
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: odata

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata:
        required: false
    - system
    - metadata
    - operation
    - entity
    - entitydata:
        required: false
    - filter:
        required: false
    - parameters:
        required: false
    - expand:
        required: false
    - errormessageonnodata:
        default: ""
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.ODataRequest
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE