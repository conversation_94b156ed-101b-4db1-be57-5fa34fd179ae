#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Run a SQL stored procedure on a database
#!
#! @prerequisites: {"if":[{"missing":["system","spname","spinput"]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Database system name to use.
#!               $type list
#!               $label Database Server
#!               $example forms-db
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input spname: The name of the stored procedure (SP) to execute
#!               $type text
#!               $label Stored Procedure Name
#!               $example spCountRowsConditional
#!               $notes This will execute the spCountRowsConditional stored procedure
#!               $required Mandatory
#! @input spinput: The input for the stored procedure (SP) specified above. A valid input provides the input and output parameters as JSON.
#!               $type text
#!               $label Stored Procedure Input
#!               $example {"whereclause":{"type":"in","value":"2=2"}, "countrows":{"type":"OUT"}}
#!               $notes This input specifies two parameters to the stored procedure. The whereclause is an in parameter to the SP and the 
#!               $notes countrows is an out parameter.  Once the SP executes with the specified whereclause, the countrows will be the 
#!               $notes result returned as defined in the optional fieldMap
#!               $required Optional
#! @input errormessageonnodata: Error message on data not found
#!               $type text
#!               $label No Data Found Error
#!               $required Optional
#!               $example Equipment not found
#!               $notes When no data is found for a database query, if errormessageonnodata is not empty, the operation is treated
#!               $notes as an error and the same is returned in the error result
#!
#! @output result: The result of the database stored procedure executed
#! @output error: The error from the database stored procedure if any
#!
#! @result SUCCESS: If the operation executed successfully
#! @result FAILURE: If there was an error while executing the operation
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: storedproc

  inputs:
    - alias
    - namespace
    - application
    - convId
    - system
    - spname
    - spinput:
        required: false
    - errormessageonnodata:
        default: ""
        required: false
  
  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.DBStoredProcedureRequest
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE