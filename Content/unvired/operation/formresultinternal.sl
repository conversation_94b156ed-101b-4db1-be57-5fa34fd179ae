#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: INTERNAL: Extracts the results and navigates correctly for all forms PA based workflows
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formparesult: The results from the forms PA based flow
#!               $type System
#!               $required Mandatory
#!               $notes System managed input.  Will be set when flow is configured
#!
#! @output stepresult: The result from the form PA based flow
#! @output steperror: The error if any 
#!
#! @result SUCCESS: If the PA was properly executed
#! @result FAILURE: If there was an error while processing
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: formresultinternal

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formparesult

  python_action:
    script: |
      try:
        import json
        steprslt = json.loads(formparesult)
        paresult = ""
        paerror = ""

        if 'result' in steprslt:
          paresult = steprslt['result']

        if 'error' in steprslt:
          paerror = steprslt['error']

      # Handle exceptions so that errors in the custom script are also caught
      except Exception as e:
        paerror = "Error in internal result: " + str(e)

  outputs:
    - stepresult: ${paresult}
    - steperror: ${paerror}

  results:
    - SUCCESS: ${paerror == ''}
    - FAILURE
