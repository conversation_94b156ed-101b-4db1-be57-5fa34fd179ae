#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Evaluates logical expressions like 3 >= 1, "a" in "abba" etc
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input priorresults: The results from earlier steps as a JSON string
#!               $type system
#!               $default {}
#!               $required Optional
#!               $example {"step_result":"1234567890"}
#! @input query: JSON representation of the condition to evaulate. The conditions are configured using the UI in the node
#!               $type system
#!               $notes A valid input is a JSON as defined in the rules UI.
#!               $required Mandatory
#! @input formdata: The currently saved form data.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $required Optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#!
#! @output result: The result of the rules evaluated as JSON string
#! @output error: The error from the expressions calculation if any
#!
#! @result THEN: If the rules were evaluated to be True
#! @result ELSE: If the rules were evaluated to be False
#! @result FAILURE: If there was an error while evaluating the rules
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: condition

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata:
        required: false
    - priorresults:
        default: ""
        required: false
    - query

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.Condition
    method_name: evaluate

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - THEN: ${result == "true"}
    - ELSE: ${result == "false"}
    - FAILURE