#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Run a JSONata expression on the source data to extract the required data
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input priorresults: The results from earlier steps as a JSON string
#!               $type system
#!               $default {}
#!               $required Optional
#!               $example {"step_result":"1234567890"}
#! @input sourcedata: The source/input JSON data to extract from.
#!               $type text
#!               $label JSON Data To Process
#!               $required Mandatory
#!               $example {"orderId":1,"customer":"CPB1000100"}
#!               $notes The most common use of this operation is to extract data from other flow steps like a REST API call etc.
#! @input mapexpression: The JSONata expression to apply on the sourcedata
#!               $type text
#!               $label Transform Expression
#!               $required Mandatory
#!               $example Any JSONata expression
#!               $notes Please check https://docs.jsonata.org/simple for details on how to build an expression
#!
#! @output result: The result of the JSONata extraction
#! @output error: The error from the JSONata extraction if any
#!
#! @result SUCCESS: If the extraction was successful
#! @result FAILURE: If there was an error while extracting the data
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: jsonparser

  inputs:
    - alias
    - namespace
    - application
    - convId
    - sourcedata
    - mapexpression
    - priorresults:
        default: "{}"
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.JsonParser
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE