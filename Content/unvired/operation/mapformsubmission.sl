#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: Maps form submission data (JSON) to an appropriate JSON structure to post to a REST endpoint or update a database etc
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Workflow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Workflow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input formdata: The input form data to transform.  Will be set by default, override if form data has been updated in workflow or as required
#!              $type text
#!              $default ${formdata}
#!              $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!              $notes The most common use of this workflow though is to transform a form submission to a format that can be saved into a database or posted to a REST webhook for example
#!              $notes Note:  The transformation works for only 2 levels of child structures (array inside an array at most)
#!              $required Mandatory
#! @input outputentitymap: The mapping information to transform the input.  The JSON data provided above is transformed to JSON output data structures based on this definition
#!              $type text
#!              $example {"order":{"pkID":"orderId","customerID":"customer","delDate":"deliveryDate","poNumber":"poNo","salesArea":"salesArea"},"orderItem":{"fkID":"orderId","pkID":"dataGrid.itemNo","matNumber":"dataGrid.material","quantity":"dataGrid.quantity","unit":"dataGrid.uom"},"orderItemItem":{"fkIdOrder":"orderId","fkID":"dataGrid.itemNo","pkID":"dataGrid.packing.id","wrap":"dataGrid.packing.wrap","box":"dataGrid.packing.box"}}
#!              $notes This is a good example to convert the form submission above to a JSON format that can be submitted to the dbrequest workflow
#!              $required Mandatory
#!
#! @output result: The result of the transformation
#! @output error: The error from the transformation if any
#!
#! @result SUCCESS: If the transformation completed successfully
#! @result FAILURE: If there was an error while transforming the data
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: mapformsubmission

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata
    - outputentitymap

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.MapFormSubmission
    method_name: transform

  outputs:
    - result: ${mapformResult}
    - error: ${mapformError}

  results:
    - SUCCESS: ${mapformError == ""}
    - FAILURE