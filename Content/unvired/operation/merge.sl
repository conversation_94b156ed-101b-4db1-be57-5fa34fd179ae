#   (c) Copyright 2025 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  A simple operation that merges a number of flow steps to a single step.
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation#!
#!
#! @output result: Merged Successfully
#! @output error: Error
#!
#! @result SUCCESS: The steps are merged successfully
#! @result FAILURE: If there was an error while merging
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: merge

  inputs:
    - alias
    - namespace
    - application
    - convId

  python_action:
    script: |
      error = ""

  outputs:
      - result: ${'Merge Success'}
      - error: ${''}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE