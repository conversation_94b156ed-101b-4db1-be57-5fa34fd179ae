#   (c) Copyright 2022 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Connect and download files and information from an FTP server.
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["system","operation"]},{"missing":[{"if":[{"==":[{"var":"operation"},"CreateDir"]},["newdirectory"]]}]},{"missing":[{"if":[{"in":[{"var":"operation"},["Get","Put","Delete"]]},["filename"]]}]},{"missing":[{"if":[{"in":[{"var":"operation"},["Rename"]]},["filename","newfilename"]]}]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The FTP server to use.
#!               $type list
#!               $label FTP Server
#!               $example ftp-server
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input operation: The action to perform on the FTP server.
#!               $type list
#!               $label FTP Action
#!               $listvalues  Get, Put, Delete, CreateDir, List, Rename
#!               $example Get
#!               $notes  Get: Download a file from the FTP server and cache it
#!               $notes Put: Upload a file from the cache
#!               $notes Delete: Delete a file on the FTP server
#!               $notes MkDir: Create a directory on the FTP server
#!               $notes List: Provide a listing of the folder on the FTP server
#!               $notes Rename: Rename or move a file on the FTP server
#!               $required Mandatory
#! @input mode: The transfer mode 
#!               $type radio
#!               $label Transfer Mode
#!               $listvalues ASCII, Binary
#!               $default ASCII
#!               $example ASCII
#!               $required Mandatory
#! @input connectionmode: The connection mode 
#!               $type radio
#!               $label Connection Mode
#!               $listvalues Active, Passive 
#!               $default Active
#!               $example Active
#!               $required Mandatory
#! @input currentdirectory: The remote directory to operate in
#!               $type text
#!               $label Remote Directory
#!               $example /home/<USER>
#!               $required Mandatory
#! @input filename: The file to upload or download.  For listing a regex to filter can be passed. For rename operation, the remote source file name with full path 
#!               $type text
#!               $label File Name
#!               $example somefile.doc
#!               $required Optional
#!               $notes  Required for operation Get, Put, Delete and Rename (Optional for List)
#! @input attachmentid: The attachment ID of the file to upload to FTP server from cache
#!               $type text
#!               $label Unique Attachment Id
#!               $example ATT111111111111111
#!               $required Optional
#!               $notes  Required for operation Put
#! @input newdirectory: The directory to create within the working directory
#!               $type text
#!               $label Remote Directory To Create
#!               $example newfldr
#!               $required Optional
#!               $notes  Required for operation MkDir
#! @input newfilename: For rename operation the remote destination file name with full path 
#!               $type text
#!               $label New File Name
#!               $example somefile.doc
#!               $required Optional
#!               $notes  Required for operation Rename
#! @input mimetype: The mime type of the file to upload
#!               $type text
#!               $label File Mime Type
#!               $example image/jpeg
#!               $required Optional
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Submission Data
#!               $required Optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#!
#! @output result: The result of the FTP operation specified
#! @output error: The error from the FTP operation if any
#!
#! @result SUCCESS: If the operation executed successfully
#! @result FAILURE: If there was an error while executing the operation
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: ftp

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata:
        required: false
    - system
    - operation
    - mode
    - currentdirectory
    - connectionmode:
        required: false
    - filename:
        required: false    
    - mimetype:
        required: false    
    - attachmentid:
        required: false    
    - newdirectory:
        required: false
    - newfilename:    
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.FTPRequest
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE