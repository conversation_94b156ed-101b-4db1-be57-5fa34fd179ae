#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Execute any adhoc JavaScript script
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input contentpath: The dynamic content path
#!               $type system
#!               $required Mandatory
#!               $example /opt/UMP5/UMP_CONTENT/content/
#!               $notes System managed input, will be automatically generated on instance
#! @input scriptname: The name of the script to execute
#!               $type system
#!               $required Mandatory
#!               $example createjson
#!               $notes System managed input, will be automatically set to stepname
#! @input inputdata: JSON object (string representation) of all the chosen inputs
#!               $type text
#!               $label Data Ro Process
#!               $example {"file":"dropbox"}
#!               $notes The selected inputs are all packaged as a JSON object and passed to the script
#!               $required Mandatory
#!
#! @output result: The result of the script execution
#! @output error: The error from the script execution if any including exceptions
#!
#! @result SUCCESS: If the script executed successfully
#! @result FAILURE: If there was an error while executing the script
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: execjavascript

  inputs:
    - alias
    - namespace
    - application
    - convId
    - contentpath
    - scriptname
    - inputdata
    - loglevel:
        default: ${get_env_var("loglevel", "error")}
        private: true

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.JSRunner
    method_name: execute

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE
