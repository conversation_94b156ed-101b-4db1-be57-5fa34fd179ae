#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: This operation records the results of the flow(s) executed so far with the Unvired Platform.  It is mandatory and should be the last step of the flow
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input outputdata: The final results of all the operations and sub-flows that have been executed.
#!               $type text
#!               $label Flow Result
#!               $example {"customerName": "Unvired Inc"}
#!               $required Optional
#!               $notes Either outputdata or outputerror has to be specified
#! @input outputerror: The error from any of the operations and/or sub-flows that have been executed.
#!               $type text
#!               $label Flow Error
#!               $example Could not find the order xxxx
#!               $required Optional
#!               $notes Either outputdata or outputerror has to be specified
#!
#! @output finalResult: The final results of all the operations and sub-flows that have been executed.
#! @output finalError: The error from any of the operations and/or sub-flows that have been executed.
#!
#! @result SUCCESS: If the operation executed successfully
#! @result FAILURE: If there was an error while executing the operation or if no outputData was provided initially
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: flowresult

  inputs:
    - alias
    - namespace
    - application
    - convId
    - outputdata:
        required: false
    - outputerror:
        required: false

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.FlowResult
    method_name: setResult

  outputs:
    - finalResult
    - finalError

  results:
    - SUCCESS: ${finalResult != ""}
    - FAILURE