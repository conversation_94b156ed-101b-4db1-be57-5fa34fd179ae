#   (c) Copyright 2021 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description: INTERNAL:  Processes the results of the flow and navigates correctly based on approval
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input approvalresult: The result from the approval
#!               $type System
#!               $required Mandatory
#!               $notes System managed input.  Will be set when flow is configured
#!
#! @output stepresult: JSON result from the approval runner
#! @output nextstep: Name of the next step in the approval process
#! @output error: The error if any 
#!
#! @result APPROVED: If the approval is completely approved
#! @result REJECTED: If the approval is rejected
#! @result RETURNED: If the approval is returned to requestor
#! @result CONTINUE: If the approval cycle has to still continue
#! @result FAILURE: If there was an error while approving
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: formapprovaldecision

  inputs:
    - alias
    - namespace
    - application
    - convId
    - approvalresult

  python_action:
    script: |
      try:
        import json
        steprslt = json.loads(approvalresult)
        nextstep = steprslt['action']
        nextstep = nextstep.lower()
        updatedformdata = steprslt['formdata']
        error = ""

      # Handle exceptions so that errors in the custom script are also caught
      except Exception as e:
        error = "Error in decision" + str(e)
        nextstep = "error"

  outputs:
    - stepformdata: ${updatedformdata}
    - nextstep: ${nextstep}
    - steperror: ${error}

  results:
    - APPROVED: ${nextstep == 'approved'}
    - REJECTED: ${nextstep == 'rejected'}
    - RETURNED: ${nextstep == 'returned'}
    - CONTINUE: ${nextstep == 'continue'}
    - FAILURE
