#   (c) Copyright 2020 Unvired Inc.
#   All rights reserved. This program and the accompanying materials
#   are made available under the terms of the Unvired SDK License.
#
#   The Unvired SDK License is available at
#   https://unvired.com/udep-sdk-license
#
#   You are free to use the content below for solely your company's licensed usage and not share
#   or pass on the content for other purposes.  Please check the license for any other limitations
#   and/or conditions governing this.  
#  
#   Please contact Unvired Inc for any support of clarifications.
#
############################################################################################################################################
#!!
#! @description:  Query a Microsoft Active Directory Service for user and other information
#!
#! @prerequisites: {"if":[{"merge":[{"missing":["system","action"]},{"missing_some":[2,{"if":[{"==":[{"var":"action"},"authenticate"]},["user","password"],[]]}]},{"missing_some":[3,{"if":[{"==":[{"var":"action"},"SetAttribute"]},["operation","field","value"],[]]}]},{"missing_some":[3,{"if":[{"==":[{"var":"action"},"SearchObjects"]},["searchcontrols","field","value"],[]]}]}]}]}
#!
#! @input alias: The company / domain of the caller
#!               $type system
#!               $example ACMECORP
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input namespace: The company /domain providing the application
#!               $type system
#!               $example UNVIRED
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input application: Name of the application executing the Flow
#!               $type system
#!               $example DIGITAL_FORMS
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input convId: Unique ID identifying this Flow execution
#!               $type system
#!               $example CNVXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
#!               $required Mandatory
#!               $notes System managed input, need not be provided by caller of the operation
#! @input system: The Active Directory (ADS) system name to use.
#!               $type list
#!               $label AD Server
#!               $example ads-server
#!               $notes The properties for the system itself is configured in a System Properties file and only the system name needs to be input
#!               $required Mandatory
#! @input action: The action to perform on the Active Directory server.
#!               $type list
#!               $label AD Action
#!               $listvalues   Authenticate,  Get Attributes, Set Attribute,   List Objects,  Search Objects
#!               $example Authenticate
#!               $notes  Authenticate: Authenticate the give domain, user and password on the ADS server
#!               $notes GetAttributes: Retrieves the given User's attributes from the ADS
#!               $notes SetAttribute: Sets the provided attrubute for the user. Attributes may be added (Add), changed (Replace) or deleted (Delete) as specified in the input
#!               $notes ListObjects: Retrieves a list of users matching the listing (input) criteria
#!               $notes SearchObjects: Searches for a list of users matching the searc (input) criteria
#!               $required Mandatory
#! @input operation: The operation to perform, required if action is SetAttributes  
#!               $type radio
#!               $label Operation
#!               $listvalues Add, Replace, Delete
#!               $example Replace
#!               $notes  attributeOperation - Adds, modifies or deletes the specified attribute with the provided value
#!               $required Optional
#! @input searchcontrols: The scope of the search to be performed, required if action is SearchObjects  
#!               $type radio
#!               $label Search Control
#!               $listvalues Object, OneLevel, SubTree
#!               $default Object
#!               $example Object
#!               $notes  Determines the scope of the ADS search, name=field name 
#!               $required Optional
#! @input field: The attibute to modify or the field to search for  
#!               $type text
#!               $label AD Attribute
#!               $default sAMAccountName
#!               $example sAMAccountName
#!               $notes  Required if the specified Action is SetAttribute or SearchObjects  
#!               $required Optional
#! @input value: The value to set for the attibute or the field value to search for  
#!               $type text
#!               $label Value
#!               $example unvired
#!               $notes  Required if the specified Action is SetAttribute or SearchObjects  
#!               $required Optional
#! @input user: ADS user id of the authenticating user  
#!               $type text
#!               $label AD Auth User
#!               $example john
#!               $notes  Can be specified in the System Properties file for search etc. For authenticating a user, set this to the username  
#!               $required Optional
#! @input password: ADS password of the authenticating user  
#!               $type text
#!               $label AD Auth Password
#!               $example xxxxxxxx
#!               $notes  Can be specified in the System Properties file for search etc. For authenticating a user, set this to the password
#!               $required Optional
#! @input basedn: base DN or point from where to search for users  
#!               $type text
#!               $label Base DN
#!               $example com.unvired/Engineering
#!               $notes  Can be specified in the System Properties file, set this if the value has to be changed for the specific call  
#!               $required Optional
#! @input domain: ADS domain of the authenticating user  
#!               $type text
#!               $label Domain
#!               $example unvired.com
#!               $notes  Can be specified in the System Properties file, set this if the value has to be changed for the specific call  
#!               $required Optional
#! @input formdata: The input form data to calculate from.  Will be set by default, override if form data has been updated in flow or as required
#!               $type text
#!               $label Form/Submission Data
#!               $required Optional
#!               $example {"orderId":1,"customer":"CPB1000100","dataGrid":[{"itemNo":10,"matDescription":"FOREIGN BEER 18 L KEG RETURNABLE","material":"CPB11600","quantity":1,"uom":"CSE","packing":[{"id":11,"wrap":true,"box":true},{"id":12,"wrap":true,"box":true}],"packing2":[{"id":12,"wrap":true,"box":true}]},{"itemNo":20,"matDescription":"FOREIGN BEER 20*0.5 L CASE - RETURNABLE","material":"CPB11100","quantity":2,"uom":"L","packing":[{"id":21,"wrap":false,"box":true}]}],"deliveryDate":"2020-01-28T00:00:00+05:30","orderType":"OR","paymentTerms":"0002","poNo":"12","salesArea":"CPB1/Sales Org. US + CB/Distrib. Cannel CPBV + 00/Cross-division","submit":true}
#!               $notes The most common use of this operation is to calculate values from form data that can later be used to setup rules or to pass as input to other flow steps
#!
#! @output result: The result of the ADS operation specified
#! @output error: The error from the ADS operation if any
#!
#! @result SUCCESS: If the operation executed successfully
#! @result FAILURE: If there was an error while executing the operation
#!!#
############################################################################################################################################
namespace: unvired.operation

operation:
  name: ads

  inputs:
    - alias
    - namespace
    - application
    - convId
    - formdata:
        required: false
    - system
    - action
    - operation:
        required: false
    - searchcontrols:
        required: false    
    - field:
        required: false    
    - value:
        required: false    
    - basedn:
        required: false    
    - domain:
        required: false    
    - user:
        required: false    
    - password:
        required: false
        sensitive: true

  java_action:
    gav: 'com.unvired:action:0.0.1'
    class_name: com.unvired.operation.ADSRequest
    method_name: query

  outputs:
    - result: ${result}
    - error: ${error}

  results:
    - SUCCESS: ${error == ""}
    - FAILURE