<!--  =======================================================================  -->
<!--  build file                                                        -->
<!--  =======================================================================  -->
<project xmlns:ivy="antlib:org.apache.ivy.ant" xmlns:sonar="antlib:org.sonar.ant" name="TURBO_APPS_CONTENT" default="build" basedir=".">
	
	 <scriptdef name="substring" language="javascript">
	     <attribute name="text" />
	     <attribute name="start" />
	     <attribute name="end" />
	     <attribute name="property" />
	     <![CDATA[
	       var text = attributes.get("text");
	       var start = attributes.get("start");
	       var end = attributes.get("end") || text.length();
	       project.setProperty(attributes.get("property"), text.substring(start, end));
	     ]]>
  	</scriptdef>

	<typedef resource="preemptive/dasho/anttask/antlib.xml"/>
	<property name="build.dir" value="${basedir}/build"/>
	<property name="dist.dir" value="${basedir}/dist"/>
	<property name="build.classes.dir" value="${build.dir}/classes"/>
	<property name="src" value="${basedir}/src"/>

	<!--  Build classpath  -->
	<path id="classpath">
		<pathelement location="${basedir}"/>
		<fileset dir="C:/Jboss/Wildfly23.0.2/modules"> <!-- Include all jars in JBOSS runtime -->
			<include name="**/*.jar"/>
		</fileset>
		<fileset dir="C:/Jboss/Wildfly23.0.2/modules-unvired"> <!-- Include all jars in Unvired modules runtime -->
			<include name="**/*.jar"/>
		</fileset>			
		<fileset dir="${basedir}/artifacts/UMP_SAP_JCO_SDK">
			<include name="**/*.jar"/>
		</fileset>
		<fileset dir="${basedir}/artifacts/UMP_ODATA_SDK">
			<include name="**/*.jar"/>
		</fileset>		
		<fileset dir="${basedir}/artifacts/UMP_JDBC_SDK">
			<include name="**/*.jar"/>
		</fileset>
		<fileset dir="${basedir}/artifacts/UMP_LOGGER_LOG4J">
			<include name="**/*.jar"/>
		</fileset>
		<fileset dir="${basedir}/artifacts/UMP_SDK">
			<include name="**/*.jar"/>
		</fileset>
		<pathelement location="${jboss.mysql.build.classes.dir}"/>
		<pathelement location="${jboss.mssql.build.classes.dir}"/>
	</path>

	<!--  Get the release number  -->
	<target name="getbuildno">
		<property environment="env"/>
		<java jar="c:/unibuild/BuildNo.jar" fork="true" failonerror="true" maxmemory="128m">
			<arg value="${env.BRANCH_TO_BUILD}/TURBO_APPS_CONTENT"/>
			<arg value="-r=true"/>
			<arg value="-n=true"/>
		</java>

		<!--  Now read into the build numberfile into release.str property  -->
		<loadfile property="release.str" srcFile="BuildNo.txt" failonerror="true"> </loadfile>
		<echo message="Using release number : ${release.str}"/>
	</target>

	<!--  ===================================================================  -->
	<!--  Prepares the build directory                                         -->
	<!--  ===================================================================  -->
	<target name="prepare">
		<property environment="env"/>
		<mkdir dir="${dist.dir}"/>
		<mkdir dir="${build.dir}"/>
		<mkdir dir="${build.classes.dir}"/>
		<antcall target="getbuildno"/>

		<!--  Now read build number from the file  -->
		<loadfile property="release.str" srcFile="BuildNo.txt" failonerror="true"> </loadfile>
		<copy overwrite="true" todir="${build.dir}/META-INF">
			<fileset dir="${src}/META-INF"/>
		</copy>

		<property environment="env"/>
		<substring text="${env.GIT_COMMIT}" start="0" end="8" property="GIT_COMMIT_ABBREV" />
		<substring text="${release.str}" start="2" end="12" property="VERSION_ABBREV" />

		<echo message="BUILD_NUMBER - BUILD_ID	: ${env.BUILD_NUMBER} - ${env.BUILD_TIMESTAMP}"/>
		<echo message="BUILD_URL				: ${env.BUILD_URL}"/>
		<echo message="GIT_REVISION				: ${GIT_COMMIT_ABBREV}"/>
		<echo message="GIT_URL					: ${env.GIT_URL} - ${env.GIT_BRANCH}"/>

		<replace file="${build.dir}/META-INF/MANIFEST.MF" token="@BUILD_NUMBER@" value='${env.BUILD_NUMBER} - ${env.BUILD_TIMESTAMP}'/>
		<replace file="${build.dir}/META-INF/MANIFEST.MF" token="@BUILD_URL@" value='${env.BUILD_URL}'/>
		<replace file="${build.dir}/META-INF/MANIFEST.MF" token="@RELEASE_NUMBER@" value="${release.str}"/>
		<replace file="${build.dir}/META-INF/MANIFEST.MF" token="@GIT_REVISION@" value='${GIT_COMMIT_ABBREV}'/>
		<replace file="${build.dir}/META-INF/MANIFEST.MF" token="@GIT_URL@" value='${env.GIT_URL} - ${env.GIT_BRANCH}'/>

		</target>

	<!--  ===================================================================  -->
	<!--  Compiles the source code                                             -->
	<!--  ===================================================================  -->
	<target name="compile" depends="prepare">
		<javac srcdir="${src}" destdir="${build.classes.dir}" debug="on" target="11" source="11" deprecation="on" optimize="off" includes="**">
			<classpath refid="classpath"/>
		</javac>
		</target>

	<target name="build" depends="compile">
		<property environment="env"/>
		<jar jarfile="${dist.dir}/${ant.project.name}.jar" manifest="${build.dir}/META-INF/MANIFEST.MF">
			<fileset dir="${build.classes.dir}">
				<include name="**"/>
			</fileset>
			<fileset dir="${basedir}">
			<include name="metadata.xml"/>
			</fileset>
		</jar>
	</target>

	<target name="publishrelease">
		<property name="pub.repo" value="apps-release"/>
		<property name="pub.repodir" value="unvired-apps-release"/>

		<!--  Now read build number from the file  -->
		<loadfile property="release.str" srcFile="BuildNo.txt" failonerror="true"> </loadfile>
		<echo message="Publishing TURBO_APPS_CONTENT to artifactory release repository: ${pub.repodir}"/>
			<ivy:settings file="ivysettings.xml" id="ivyrelease"/>
			<ivy:resolve file="ivy.xml" revision="${release.str}"/>
			<ivy:publish resolver="artifactory-publish" revision="${release.str}" update="true" settingsRef="ivyrelease">
			<artifacts pattern="${dist.dir}/[artifact].[ext]"/>
		</ivy:publish>
	</target>

	<target name="publish" depends="build">
		<property environment="env"/>
		<antcall target="publishrelease"/>
	</target>

</project>
